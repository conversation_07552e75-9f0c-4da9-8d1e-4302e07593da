from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QTableWidget, QTableWidgetItem, QHeaderView, QLabel,
                             QLineEdit, QComboBox, QDateEdit, QDialog, QFormLayout,
                             QDialogButtonBox, QMessageBox, QFrame, QSpinBox,
                             QDoubleSpinBox, QTextEdit)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import date, datetime
from models import Expense, ExpenseValidator

class AddExpenseDialog(QDialog):
    """نافذة إضافة مصروف جديد"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إضافة مصروف جديد")
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # نموذج الإدخال
        form_layout = QFormLayout()
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ريال")
        form_layout.addRow("المبلغ:", self.amount_input)
        
        # الوصف
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("وصف المصروف...")
        form_layout.addRow("الوصف:", self.description_input)
        
        # الفئة
        self.category_combo = QComboBox()
        self.load_categories()
        form_layout.addRow("الفئة:", self.category_combo)
        
        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_input)
        
        # ملاحظات إضافية
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)...")
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Save).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
        buttons.accepted.connect(self.save_expense)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
    def load_categories(self):
        """تحميل الفئات في القائمة المنسدلة"""
        categories = self.db.get_categories()
        self.category_combo.clear()
        
        for category in categories:
            self.category_combo.addItem(category[1], category[0])  # النص، المعرف
            
    def save_expense(self):
        """حفظ المصروف الجديد"""
        # جمع البيانات
        amount = self.amount_input.value()
        description = self.description_input.text().strip()
        category_id = self.category_combo.currentData()
        expense_date = self.date_input.date().toPyDate()
        
        # إضافة الملاحظات للوصف إذا وجدت
        notes = self.notes_input.toPlainText().strip()
        if notes:
            description += f" - {notes}"
        
        # إنشاء كائن المصروف للتحقق
        expense = Expense(
            amount=amount,
            description=description,
            category_id=category_id,
            date=expense_date
        )
        
        # التحقق من صحة البيانات
        errors = ExpenseValidator.validate_expense(expense)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
        
        try:
            # حفظ في قاعدة البيانات
            self.db.add_expense(amount, description, category_id, expense_date)
            QMessageBox.information(self, "نجح", "تم إضافة المصروف بنجاح!")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المصروف:\n{str(e)}")

class ExpenseWidget(QWidget):
    """واجهة إدارة المصاريف"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        self.load_expenses()
        
    def init_ui(self):
        """إعداد واجهة المصاريف"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title = QLabel("إدارة المصاريف")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة مصروف جديد
        add_btn = QPushButton("➕ إضافة مصروف")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_btn.clicked.connect(self.add_expense)
        toolbar_layout.addWidget(add_btn)
        
        # فلاتر البحث
        toolbar_layout.addStretch()
        
        # فلتر الفئة
        category_label = QLabel("الفئة:")
        toolbar_layout.addWidget(category_label)
        
        self.category_filter = QComboBox()
        self.category_filter.addItem("جميع الفئات", None)
        self.load_categories_filter()
        self.category_filter.currentTextChanged.connect(self.filter_expenses)
        toolbar_layout.addWidget(self.category_filter)
        
        # فلتر التاريخ
        date_label = QLabel("من:")
        toolbar_layout.addWidget(date_label)
        
        self.start_date_filter = QDateEdit()
        self.start_date_filter.setDate(QDate.currentDate().addDays(-30))
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.dateChanged.connect(self.filter_expenses)
        toolbar_layout.addWidget(self.start_date_filter)
        
        to_label = QLabel("إلى:")
        toolbar_layout.addWidget(to_label)
        
        self.end_date_filter = QDateEdit()
        self.end_date_filter.setDate(QDate.currentDate())
        self.end_date_filter.setCalendarPopup(True)
        self.end_date_filter.dateChanged.connect(self.filter_expenses)
        toolbar_layout.addWidget(self.end_date_filter)
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # جدول المصاريف
        self.expenses_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.expenses_table)
        
        # شريط الإحصائيات
        self.stats_frame = QFrame()
        self.stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                border-left: 4px solid #3498db;
            }
        """)
        stats_layout = QHBoxLayout(self.stats_frame)
        
        self.total_label = QLabel("الإجمالي: 0.00 ريال")
        self.total_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        stats_layout.addWidget(self.total_label)
        
        stats_layout.addStretch()
        
        self.count_label = QLabel("عدد المصاريف: 0")
        self.count_label.setStyleSheet("font-size: 14px; color: #7f8c8d;")
        stats_layout.addWidget(self.count_label)
        
        layout.addWidget(self.stats_frame)
        
    def setup_table(self):
        """إعداد جدول المصاريف"""
        headers = ["المعرف", "المبلغ", "الوصف", "الفئة", "التاريخ", "الإجراءات"]
        self.expenses_table.setColumnCount(len(headers))
        self.expenses_table.setHorizontalHeaderLabels(headers)
        
        # إعداد عرض الأعمدة
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # المعرف
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # الوصف
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الفئة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الإجراءات
        
        # إخفاء عمود المعرف
        self.expenses_table.setColumnHidden(0, True)
        
        # إعداد الأنماط
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border-radius: 8px;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        
    def load_categories_filter(self):
        """تحميل الفئات في فلتر البحث"""
        categories = self.db.get_categories()
        for category in categories:
            self.category_filter.addItem(category[1], category[0])
            
    def load_expenses(self):
        """تحميل المصاريف في الجدول"""
        # الحصول على فلاتر البحث
        category_id = self.category_filter.currentData()
        start_date = self.start_date_filter.date().toPyDate()
        end_date = self.end_date_filter.date().toPyDate()
        
        # جلب المصاريف من قاعدة البيانات
        expenses = self.db.get_expenses(start_date, end_date, category_id)
        
        # تحديث الجدول
        self.expenses_table.setRowCount(len(expenses))
        
        total_amount = 0
        for row, expense in enumerate(expenses):
            expense_id, amount, description, expense_date, category_name, category_color = expense
            total_amount += amount
            
            # إضافة البيانات للجدول
            self.expenses_table.setItem(row, 0, QTableWidgetItem(str(expense_id)))
            self.expenses_table.setItem(row, 1, QTableWidgetItem(f"{amount:,.2f} ريال"))
            self.expenses_table.setItem(row, 2, QTableWidgetItem(description))
            self.expenses_table.setItem(row, 3, QTableWidgetItem(category_name or "غير محدد"))
            self.expenses_table.setItem(row, 4, QTableWidgetItem(expense_date))
            
            # زر الحذف
            delete_btn = QPushButton("🗑️ حذف")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            delete_btn.clicked.connect(lambda checked, eid=expense_id: self.delete_expense(eid))
            self.expenses_table.setCellWidget(row, 5, delete_btn)
        
        # تحديث الإحصائيات
        self.update_stats(total_amount, len(expenses))
        
    def update_stats(self, total_amount, count):
        """تحديث شريط الإحصائيات"""
        self.total_label.setText(f"الإجمالي: {total_amount:,.2f} ريال")
        self.count_label.setText(f"عدد المصاريف: {count}")
        
    def add_expense(self):
        """فتح نافذة إضافة مصروف جديد"""
        dialog = AddExpenseDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            
    def delete_expense(self, expense_id):
        """حذف مصروف"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا المصروف؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db.delete_expense(expense_id)
                QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح!")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المصروف:\n{str(e)}")
                
    def filter_expenses(self):
        """تطبيق فلاتر البحث"""
        self.load_expenses()
        
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_expenses()
