#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة المصاريف الشخصية والديون
تطبيق سطح مكتب باستخدام PyQt5 وقاعدة بيانات SQLite

المطور: Augment Agent
التاريخ: 2025
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont
from main_window import MainWindow

def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    
    # إعداد معلومات التطبيق
    app.setApplicationName("مدير المصاريف الشخصية")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Personal Finance Manager")
    
    # إعداد اتجاه النص للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط الافتراضي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إعداد الأنماط العامة
    app.setStyleSheet("""
        QApplication {
            font-family: "Arial", "Tahoma", "DejaVu Sans";
        }
        QMessageBox {
            font-size: 12px;
        }
        QMessageBox QLabel {
            font-size: 12px;
        }
        QMessageBox QPushButton {
            font-size: 11px;
            padding: 8px 16px;
            min-width: 80px;
        }
    """)
    
    return app

def show_splash_screen():
    """عرض شاشة البداية"""
    # إنشاء شاشة بداية بسيطة
    splash = QSplashScreen()
    splash.setStyleSheet("""
        QSplashScreen {
            background-color: #2c3e50;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }
    """)
    
    # إضافة نص للشاشة
    splash.showMessage("جاري تحميل مدير المصاريف الشخصية...", 
                      Qt.AlignCenter | Qt.AlignBottom, Qt.white)
    splash.show()
    
    return splash

def check_dependencies():
    """التحقق من المتطلبات المطلوبة"""
    missing_modules = []
    
    try:
        import PyQt5
    except ImportError:
        missing_modules.append("PyQt5")
    
    try:
        import matplotlib
    except ImportError:
        missing_modules.append("matplotlib")
    
    try:
        import sqlite3
    except ImportError:
        missing_modules.append("sqlite3")
    
    if missing_modules:
        error_msg = f"""
المكتبات التالية مطلوبة لتشغيل البرنامج:
{', '.join(missing_modules)}

يرجى تثبيتها باستخدام الأمر:
pip install {' '.join(missing_modules)}
"""
        return False, error_msg
    
    return True, ""

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "data",
        "exports",
        "backups"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
            except Exception as e:
                print(f"تعذر إنشاء المجلد {directory}: {e}")

def handle_exception(exc_type, exc_value, exc_traceback):
    """معالج الأخطاء العام"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = f"""
حدث خطأ غير متوقع في التطبيق:

نوع الخطأ: {exc_type.__name__}
رسالة الخطأ: {str(exc_value)}

يرجى إعادة تشغيل التطبيق. إذا استمر الخطأ، يرجى التواصل مع الدعم الفني.
"""
    
    # عرض رسالة الخطأ للمستخدم
    app = QApplication.instance()
    if app:
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("خطأ في التطبيق")
        msg_box.setText("حدث خطأ غير متوقع")
        msg_box.setDetailedText(error_msg)
        msg_box.exec_()
    
    # طباعة الخطأ في وحدة التحكم للمطورين
    sys.__excepthook__(exc_type, exc_value, exc_traceback)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # التحقق من المتطلبات
        deps_ok, error_msg = check_dependencies()
        if not deps_ok:
            print(error_msg)
            return 1
        
        # إنشاء المجلدات المطلوبة
        create_directories()
        
        # إعداد التطبيق
        app = setup_application()
        
        # إعداد معالج الأخطاء
        sys.excepthook = handle_exception
        
        # عرض شاشة البداية
        splash = show_splash_screen()
        
        # تأخير قصير لعرض شاشة البداية
        QTimer.singleShot(2000, splash.close)
        
        # إنشاء النافذة الرئيسية
        try:
            main_window = MainWindow()
            
            # إخفاء شاشة البداية وعرض النافذة الرئيسية
            QTimer.singleShot(2000, lambda: [splash.close(), main_window.show()])
            
        except Exception as e:
            splash.close()
            QMessageBox.critical(None, "خطأ في التهيئة", 
                               f"فشل في تهيئة التطبيق:\n{str(e)}")
            return 1
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    # تشغيل التطبيق
    exit_code = main()
    sys.exit(exit_code)
