import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="expense_manager.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """إنشاء الجداول الأساسية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول فئات المصاريف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                color TEXT DEFAULT '#3498db',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المصاريف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                amount REAL NOT NULL,
                description TEXT NOT NULL,
                category_id INTEGER,
                date DATE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # جدول الديون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS debts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_name TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                debt_type TEXT NOT NULL CHECK (debt_type IN ('لي', 'عليّ')),
                due_date DATE,
                is_paid BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                paid_at TIMESTAMP NULL
            )
        ''')
        
        # جدول دفعات الديون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS debt_payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                debt_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_date DATE NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (debt_id) REFERENCES debts (id)
            )
        ''')
        
        conn.commit()
        
        # إضافة فئات افتراضية
        self.add_default_categories(cursor)
        conn.commit()
        conn.close()
    
    def add_default_categories(self, cursor):
        """إضافة فئات افتراضية"""
        default_categories = [
            ('طعام', 'مصاريف الطعام والمشروبات', '#e74c3c'),
            ('مواصلات', 'مصاريف النقل والمواصلات', '#f39c12'),
            ('فواتير', 'فواتير الكهرباء والماء والهاتف', '#9b59b6'),
            ('ترفيه', 'مصاريف الترفيه والتسلية', '#2ecc71'),
            ('صحة', 'مصاريف طبية وصحية', '#1abc9c'),
            ('تسوق', 'مشتريات عامة', '#34495e'),
            ('تعليم', 'مصاريف تعليمية', '#3498db'),
            ('أخرى', 'مصاريف متنوعة', '#95a5a6')
        ]
        
        for name, desc, color in default_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (name, description, color)
                VALUES (?, ?, ?)
            ''', (name, desc, color))
    
    # وظائف المصاريف
    def add_expense(self, amount, description, category_id, date=None):
        """إضافة مصروف جديد"""
        if date is None:
            date = datetime.now().date()
        
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO expenses (amount, description, category_id, date)
            VALUES (?, ?, ?, ?)
        ''', (amount, description, category_id, date))
        conn.commit()
        expense_id = cursor.lastrowid
        conn.close()
        return expense_id
    
    def get_expenses(self, start_date=None, end_date=None, category_id=None):
        """جلب المصاريف مع إمكانية التصفية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT e.id, e.amount, e.description, e.date, c.name as category_name, c.color
            FROM expenses e
            LEFT JOIN categories c ON e.category_id = c.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND e.date >= ?'
            params.append(start_date)
        
        if end_date:
            query += ' AND e.date <= ?'
            params.append(end_date)
        
        if category_id:
            query += ' AND e.category_id = ?'
            params.append(category_id)
        
        query += ' ORDER BY e.date DESC'
        
        cursor.execute(query, params)
        expenses = cursor.fetchall()
        conn.close()
        return expenses
    
    def delete_expense(self, expense_id):
        """حذف مصروف"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('DELETE FROM expenses WHERE id = ?', (expense_id,))
        conn.commit()
        conn.close()
    
    # وظائف الفئات
    def get_categories(self):
        """جلب جميع الفئات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, description, color FROM categories ORDER BY name')
        categories = cursor.fetchall()
        conn.close()
        return categories
    
    def add_category(self, name, description='', color='#3498db'):
        """إضافة فئة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO categories (name, description, color)
            VALUES (?, ?, ?)
        ''', (name, description, color))
        conn.commit()
        category_id = cursor.lastrowid
        conn.close()
        return category_id
    
    # وظائف الديون
    def add_debt(self, person_name, amount, description, debt_type, due_date=None):
        """إضافة دين جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO debts (person_name, amount, description, debt_type, due_date)
            VALUES (?, ?, ?, ?, ?)
        ''', (person_name, amount, description, debt_type, due_date))
        conn.commit()
        debt_id = cursor.lastrowid
        conn.close()
        return debt_id
    
    def get_debts(self, debt_type=None, is_paid=None):
        """جلب الديون مع إمكانية التصفية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT id, person_name, amount, description, debt_type, due_date, is_paid, created_at
            FROM debts
            WHERE 1=1
        '''
        params = []
        
        if debt_type:
            query += ' AND debt_type = ?'
            params.append(debt_type)
        
        if is_paid is not None:
            query += ' AND is_paid = ?'
            params.append(is_paid)
        
        query += ' ORDER BY due_date ASC, created_at DESC'
        
        cursor.execute(query, params)
        debts = cursor.fetchall()
        conn.close()
        return debts
    
    def mark_debt_paid(self, debt_id):
        """تحديد دين كمسدد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE debts 
            SET is_paid = TRUE, paid_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (debt_id,))
        conn.commit()
        conn.close()
    
    def get_expense_summary(self, start_date=None, end_date=None):
        """جلب ملخص المصاريف حسب الفئة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT c.name, c.color, SUM(e.amount) as total, COUNT(e.id) as count
            FROM expenses e
            LEFT JOIN categories c ON e.category_id = c.id
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND e.date >= ?'
            params.append(start_date)
        
        if end_date:
            query += ' AND e.date <= ?'
            params.append(end_date)
        
        query += ' GROUP BY c.id, c.name, c.color ORDER BY total DESC'
        
        cursor.execute(query, params)
        summary = cursor.fetchall()
        conn.close()
        return summary
