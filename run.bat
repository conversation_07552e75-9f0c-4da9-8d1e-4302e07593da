@echo off
echo تشغيل مدير المصاريف الشخصية...
echo ================================

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.7 أو أحدث من https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    pause
    exit /b 1
)

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo جاري التحقق من المتطلبات...
pip install -r requirements.txt

REM تشغيل التطبيق
echo جاري تشغيل التطبيق...
python main.py

pause
