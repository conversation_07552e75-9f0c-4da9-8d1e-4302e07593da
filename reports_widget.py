from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QLabel, QFrame, QComboBox, QDateEdit, QScrollArea,
                             QGridLayout, QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QMessageBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor, QPainter, QPen
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib
from datetime import date, datetime, timedelta
from calendar import monthrange

# إعداد matplotlib للعربية
matplotlib.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ChartWidget(QWidget):
    """واجهة الرسوم البيانية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        
        layout = QVBoxLayout(self)
        layout.addWidget(self.canvas)
        
    def clear_chart(self):
        """مسح الرسم البياني"""
        self.figure.clear()
        self.canvas.draw()
        
    def create_pie_chart(self, data, title):
        """إنشاء رسم بياني دائري"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        if not data:
            ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', 
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=14)
            self.canvas.draw()
            return
        
        labels = [item[0] for item in data]
        sizes = [item[2] for item in data]  # المبلغ الإجمالي
        colors = [item[1] for item in data]  # اللون
        
        # إنشاء الرسم الدائري
        wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                         autopct='%1.1f%%', startangle=90)
        
        # تحسين النصوص
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        # إضافة وسيلة إيضاح
        ax.legend(wedges, [f"{label}: {size:,.0f} ريال" for label, size in zip(labels, sizes)],
                 title="الفئات", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
        
        self.figure.tight_layout()
        self.canvas.draw()
        
    def create_bar_chart(self, data, title, xlabel, ylabel):
        """إنشاء رسم بياني بالأعمدة"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        if not data:
            ax.text(0.5, 0.5, 'لا توجد بيانات للعرض', 
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=14)
            self.canvas.draw()
            return
        
        labels = [item[0] for item in data]
        values = [item[1] for item in data]
        colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#95a5a6']
        
        bars = ax.bar(labels, values, color=colors[:len(labels)])
        
        # إضافة قيم على الأعمدة
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                   f'{value:,.0f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(xlabel, fontsize=12)
        ax.set_ylabel(ylabel, fontsize=12)
        
        # تدوير تسميات المحور السيني إذا كانت طويلة
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        self.figure.tight_layout()
        self.canvas.draw()

class ReportsWidget(QWidget):
    """واجهة التقارير والإحصائيات"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة التقارير"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title = QLabel("التقارير والإحصائيات")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # فلتر الفترة الزمنية
        period_label = QLabel("الفترة:")
        toolbar_layout.addWidget(period_label)
        
        self.period_combo = QComboBox()
        self.period_combo.addItem("هذا الشهر", "current_month")
        self.period_combo.addItem("الشهر الماضي", "last_month")
        self.period_combo.addItem("آخر 3 أشهر", "last_3_months")
        self.period_combo.addItem("هذه السنة", "current_year")
        self.period_combo.addItem("السنة الماضية", "last_year")
        self.period_combo.addItem("فترة مخصصة", "custom")
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        toolbar_layout.addWidget(self.period_combo)
        
        # تواريخ مخصصة
        self.start_date_input = QDateEdit()
        self.start_date_input.setDate(QDate.currentDate().addDays(-30))
        self.start_date_input.setCalendarPopup(True)
        self.start_date_input.setVisible(False)
        self.start_date_input.dateChanged.connect(self.refresh_data)
        toolbar_layout.addWidget(self.start_date_input)
        
        self.end_date_input = QDateEdit()
        self.end_date_input.setDate(QDate.currentDate())
        self.end_date_input.setCalendarPopup(True)
        self.end_date_input.setVisible(False)
        self.end_date_input.dateChanged.connect(self.refresh_data)
        toolbar_layout.addWidget(self.end_date_input)
        
        toolbar_layout.addStretch()
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(refresh_btn)
        
        # زر تصدير
        export_btn = QPushButton("📊 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        export_btn.clicked.connect(self.export_report)
        toolbar_layout.addWidget(export_btn)
        
        layout.addLayout(toolbar_layout)
        
        # تبويبات التقارير
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
        """)
        
        # تبويب ملخص المصاريف
        self.expenses_summary_tab = self.create_expenses_summary_tab()
        self.tabs.addTab(self.expenses_summary_tab, "📊 ملخص المصاريف")
        
        # تبويب تحليل الديون
        self.debts_analysis_tab = self.create_debts_analysis_tab()
        self.tabs.addTab(self.debts_analysis_tab, "💰 تحليل الديون")
        
        # تبويب الاتجاهات الزمنية
        self.trends_tab = self.create_trends_tab()
        self.tabs.addTab(self.trends_tab, "📈 الاتجاهات")
        
        layout.addWidget(self.tabs)
        
    def create_expenses_summary_tab(self):
        """إنشاء تبويب ملخص المصاريف"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # بطاقات الإحصائيات
        stats_layout = QHBoxLayout()
        
        self.total_expenses_card = self.create_stat_card("💰", "إجمالي المصاريف", "0.00 ريال")
        stats_layout.addWidget(self.total_expenses_card)
        
        self.avg_daily_card = self.create_stat_card("📅", "متوسط يومي", "0.00 ريال")
        stats_layout.addWidget(self.avg_daily_card)
        
        self.top_category_card = self.create_stat_card("🏆", "أعلى فئة", "غير محدد")
        stats_layout.addWidget(self.top_category_card)
        
        self.expenses_count_card = self.create_stat_card("📝", "عدد المصاريف", "0")
        stats_layout.addWidget(self.expenses_count_card)
        
        layout.addLayout(stats_layout)
        
        # الرسم البياني الدائري للفئات
        self.expenses_chart = ChartWidget()
        layout.addWidget(self.expenses_chart)
        
        return widget
        
    def create_debts_analysis_tab(self):
        """إنشاء تبويب تحليل الديون"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # بطاقات إحصائيات الديون
        debts_stats_layout = QHBoxLayout()
        
        self.my_debts_card = self.create_stat_card("⬆️", "ديون لي", "0.00 ريال")
        debts_stats_layout.addWidget(self.my_debts_card)
        
        self.on_me_debts_card = self.create_stat_card("⬇️", "ديون عليّ", "0.00 ريال")
        debts_stats_layout.addWidget(self.on_me_debts_card)
        
        self.net_balance_card = self.create_stat_card("⚖️", "الرصيد الصافي", "0.00 ريال")
        debts_stats_layout.addWidget(self.net_balance_card)
        
        self.overdue_debts_card = self.create_stat_card("⚠️", "ديون متأخرة", "0")
        debts_stats_layout.addWidget(self.overdue_debts_card)
        
        layout.addLayout(debts_stats_layout)
        
        # جدول الديون المستحقة قريباً
        upcoming_frame = QFrame()
        upcoming_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        upcoming_layout = QVBoxLayout(upcoming_frame)
        
        upcoming_title = QLabel("الديون المستحقة خلال الأسبوع القادم")
        upcoming_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        upcoming_layout.addWidget(upcoming_title)
        
        self.upcoming_debts_table = QTableWidget()
        self.setup_upcoming_debts_table()
        upcoming_layout.addWidget(self.upcoming_debts_table)
        
        layout.addWidget(upcoming_frame)
        
        return widget
        
    def create_trends_tab(self):
        """إنشاء تبويب الاتجاهات الزمنية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # رسم بياني للاتجاهات الشهرية
        self.trends_chart = ChartWidget()
        layout.addWidget(self.trends_chart)
        
        return widget

    def create_stat_card(self, icon, title, value):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #3498db;
                min-height: 100px;
            }
        """)

        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px; margin-bottom: 10px;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #7f8c8d; margin-bottom: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # حفظ مرجع للتحديث لاحقاً
        card.value_label = value_label

        return card

    def setup_upcoming_debts_table(self):
        """إعداد جدول الديون المستحقة قريباً"""
        headers = ["الشخص", "المبلغ", "النوع", "تاريخ الاستحقاق", "الأيام المتبقية"]
        self.upcoming_debts_table.setColumnCount(len(headers))
        self.upcoming_debts_table.setHorizontalHeaderLabels(headers)

        # إعداد عرض الأعمدة
        header = self.upcoming_debts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

        self.upcoming_debts_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: none;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                color: #2c3e50;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        self.upcoming_debts_table.setMaximumHeight(200)

    def get_date_range(self):
        """الحصول على نطاق التاريخ المحدد"""
        period = self.period_combo.currentData()
        today = date.today()

        if period == "current_month":
            start_date = date(today.year, today.month, 1)
            end_date = today
        elif period == "last_month":
            if today.month == 1:
                start_date = date(today.year - 1, 12, 1)
                end_date = date(today.year - 1, 12, 31)
            else:
                start_date = date(today.year, today.month - 1, 1)
                last_day = monthrange(today.year, today.month - 1)[1]
                end_date = date(today.year, today.month - 1, last_day)
        elif period == "last_3_months":
            start_date = today - timedelta(days=90)
            end_date = today
        elif period == "current_year":
            start_date = date(today.year, 1, 1)
            end_date = today
        elif period == "last_year":
            start_date = date(today.year - 1, 1, 1)
            end_date = date(today.year - 1, 12, 31)
        else:  # custom
            start_date = self.start_date_input.date().toPyDate()
            end_date = self.end_date_input.date().toPyDate()

        return start_date, end_date

    def on_period_changed(self):
        """عند تغيير الفترة الزمنية"""
        is_custom = self.period_combo.currentData() == "custom"
        self.start_date_input.setVisible(is_custom)
        self.end_date_input.setVisible(is_custom)

        if not is_custom:
            self.refresh_data()

    def refresh_data(self):
        """تحديث جميع البيانات"""
        self.update_expenses_summary()
        self.update_debts_analysis()
        self.update_trends()

    def update_expenses_summary(self):
        """تحديث ملخص المصاريف"""
        start_date, end_date = self.get_date_range()

        # جلب ملخص المصاريف
        summary = self.db.get_expense_summary(start_date, end_date)
        expenses = self.db.get_expenses(start_date, end_date)

        # حساب الإحصائيات
        total_amount = sum(expense[1] for expense in expenses)
        expenses_count = len(expenses)

        # متوسط يومي
        days_diff = (end_date - start_date).days + 1
        avg_daily = total_amount / days_diff if days_diff > 0 else 0

        # أعلى فئة
        top_category = summary[0][0] if summary else "غير محدد"

        # تحديث البطاقات
        self.total_expenses_card.value_label.setText(f"{total_amount:,.2f} ريال")
        self.avg_daily_card.value_label.setText(f"{avg_daily:,.2f} ريال")
        self.top_category_card.value_label.setText(top_category)
        self.expenses_count_card.value_label.setText(str(expenses_count))

        # تحديث الرسم البياني
        if summary:
            self.expenses_chart.create_pie_chart(summary, "توزيع المصاريف حسب الفئة")
        else:
            self.expenses_chart.clear_chart()

    def update_debts_analysis(self):
        """تحديث تحليل الديون"""
        # جلب الديون غير المسددة
        pending_debts = self.db.get_debts(is_paid=False)

        my_debts_total = 0
        on_me_debts_total = 0
        overdue_count = 0
        upcoming_debts = []

        today = date.today()
        next_week = today + timedelta(days=7)

        for debt in pending_debts:
            debt_id, person_name, amount, description, debt_type, due_date, is_paid, created_at = debt

            if debt_type == "لي":
                my_debts_total += amount
            else:
                on_me_debts_total += amount

            # التحقق من الديون المتأخرة والمستحقة قريباً
            if due_date:
                try:
                    due_date_obj = datetime.strptime(due_date, "%Y-%m-%d").date()
                    if due_date_obj < today:
                        overdue_count += 1
                    elif due_date_obj <= next_week:
                        days_remaining = (due_date_obj - today).days
                        upcoming_debts.append((person_name, amount, debt_type, due_date, days_remaining))
                except:
                    pass

        # الرصيد الصافي
        net_balance = my_debts_total - on_me_debts_total

        # تحديث البطاقات
        self.my_debts_card.value_label.setText(f"{my_debts_total:,.2f} ريال")
        self.on_me_debts_card.value_label.setText(f"{on_me_debts_total:,.2f} ريال")

        net_text = f"{abs(net_balance):,.2f} ريال"
        if net_balance > 0:
            net_text += " (لصالحي)"
        elif net_balance < 0:
            net_text += " (عليّ)"
        else:
            net_text = "متوازن"

        self.net_balance_card.value_label.setText(net_text)
        self.overdue_debts_card.value_label.setText(str(overdue_count))

        # تحديث جدول الديون المستحقة قريباً
        self.upcoming_debts_table.setRowCount(len(upcoming_debts))
        for row, debt_info in enumerate(upcoming_debts):
            person_name, amount, debt_type, due_date, days_remaining = debt_info

            self.upcoming_debts_table.setItem(row, 0, QTableWidgetItem(person_name))
            self.upcoming_debts_table.setItem(row, 1, QTableWidgetItem(f"{amount:,.2f} ريال"))
            self.upcoming_debts_table.setItem(row, 2, QTableWidgetItem(debt_type))
            self.upcoming_debts_table.setItem(row, 3, QTableWidgetItem(due_date))

            days_text = f"{days_remaining} يوم"
            if days_remaining == 0:
                days_text = "اليوم"
            elif days_remaining == 1:
                days_text = "غداً"

            days_item = QTableWidgetItem(days_text)
            if days_remaining <= 1:
                days_item.setBackground(QColor("#fadbd8"))  # أحمر فاتح
            elif days_remaining <= 3:
                days_item.setBackground(QColor("#fdeaa7"))  # أصفر فاتح

            self.upcoming_debts_table.setItem(row, 4, days_item)

    def update_trends(self):
        """تحديث الاتجاهات الزمنية"""
        # جلب المصاريف الشهرية للسنة الحالية
        current_year = date.today().year
        monthly_data = []

        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

        for month in range(1, 13):
            start_date = date(current_year, month, 1)
            last_day = monthrange(current_year, month)[1]
            end_date = date(current_year, month, last_day)

            expenses = self.db.get_expenses(start_date, end_date)
            total = sum(expense[1] for expense in expenses)

            monthly_data.append((months[month-1], total))

        # إنشاء الرسم البياني
        self.trends_chart.create_bar_chart(monthly_data,
                                          f"اتجاه المصاريف الشهرية - {current_year}",
                                          "الشهر", "المبلغ (ريال)")

    def export_report(self):
        """تصدير التقرير"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التقرير", f"تقرير_المصاريف_{date.today()}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                start_date, end_date = self.get_date_range()

                # إنشاء التقرير النصي
                report_content = self.generate_text_report(start_date, end_date)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                QMessageBox.information(self, "نجح", f"تم حفظ التقرير في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تصدير التقرير:\n{str(e)}")

    def generate_text_report(self, start_date, end_date):
        """إنشاء تقرير نصي"""
        report = f"""
تقرير المصاريف الشخصية والديون
=====================================

الفترة: من {start_date} إلى {end_date}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

ملخص المصاريف:
---------------
"""

        # إضافة بيانات المصاريف
        expenses = self.db.get_expenses(start_date, end_date)
        summary = self.db.get_expense_summary(start_date, end_date)

        total_amount = sum(expense[1] for expense in expenses)
        report += f"إجمالي المصاريف: {total_amount:,.2f} ريال\n"
        report += f"عدد المصاريف: {len(expenses)}\n\n"

        report += "توزيع المصاريف حسب الفئة:\n"
        for category_name, color, total, count in summary:
            percentage = (total / total_amount * 100) if total_amount > 0 else 0
            report += f"- {category_name}: {total:,.2f} ريال ({percentage:.1f}%)\n"

        # إضافة بيانات الديون
        pending_debts = self.db.get_debts(is_paid=False)
        my_debts_total = sum(debt[2] for debt in pending_debts if debt[4] == "لي")
        on_me_debts_total = sum(debt[2] for debt in pending_debts if debt[4] == "عليّ")

        report += f"""
ملخص الديون:
-------------
ديون لي: {my_debts_total:,.2f} ريال
ديون عليّ: {on_me_debts_total:,.2f} ريال
الرصيد الصافي: {my_debts_total - on_me_debts_total:,.2f} ريال

عدد الديون المعلقة: {len(pending_debts)}
"""

        return report
