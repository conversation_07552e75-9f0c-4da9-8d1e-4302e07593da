"""
مكونات واجهة المستخدم المحسنة
يحتوي على مكونات UI قابلة لإعادة الاستخدام
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QFrame, QGraphicsDropShadowEffect)
from PyQt5.QtCore import Qt, QRect, QPropertyAnimation, pyqtProperty, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QPainter, QPen, QBrush, QLinearGradient

class AnimatedButton(QPushButton):
    """زر متحرك مع تأثيرات بصرية"""
    
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setMinimumHeight(50)
        self.setCursor(Qt.PointingHandCursor)
        
        # إضافة ظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
        
        # الأنماط الأساسية
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
                padding: 15px 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
        """)

class GlowCard(QFrame):
    """بطاقة مع تأثير توهج"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.NoFrame)
        
        # إضافة ظل متوهج
        glow = QGraphicsDropShadowEffect()
        glow.setBlurRadius(20)
        glow.setColor(QColor(52, 152, 219, 100))
        glow.setOffset(0, 0)
        self.setGraphicsEffect(glow)
        
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.95), stop:1 rgba(248,249,250,0.95));
                border-radius: 20px;
                border: 2px solid rgba(52, 152, 219, 0.3);
                padding: 20px;
            }
        """)

class StatCard(QFrame):
    """بطاقة إحصائية محسنة مع أنيميشن"""
    
    def __init__(self, icon, title, value, color="#3498db", parent=None):
        super().__init__(parent)
        self.color = color
        self.setup_ui(icon, title, value)
        self.setup_animations()
        
    def setup_ui(self, icon, title, value):
        """إعداد واجهة البطاقة"""
        self.setFixedSize(280, 140)
        self.setCursor(Qt.PointingHandCursor)
        
        # إضافة ظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(25, 20, 25, 20)
        
        # الصف العلوي
        top_row = QHBoxLayout()
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                color: {self.color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.color}20, stop:1 {self.color}10);
                border-radius: 15px;
                padding: 12px;
                min-width: 60px;
                max-width: 60px;
                text-align: center;
            }}
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        top_row.addWidget(icon_label)
        
        top_row.addStretch()
        
        # مؤشر الاتجاه
        trend_label = QLabel("📈")
        trend_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                color: {self.color};
                background: {self.color}15;
                border-radius: 8px;
                padding: 8px;
            }}
        """)
        top_row.addWidget(trend_label)
        
        layout.addLayout(top_row)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                font-weight: 500;
                margin: 5px 0px;
            }
        """)
        layout.addWidget(title_label)
        
        # القيمة
        self.value_label = QLabel(value)
        self.value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 22px;
                font-weight: bold;
                color: {self.color};
                margin-bottom: 5px;
            }}
        """)
        layout.addWidget(self.value_label)
        
        # الأنماط الأساسية
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border-radius: 18px;
                border: 2px solid #e9ecef;
            }}
            QFrame:hover {{
                border: 3px solid {self.color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 #f1f2f6);
            }}
        """)
        
    def setup_animations(self):
        """إعداد الأنيميشن"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def enterEvent(self, event):
        """عند دخول الماوس"""
        current_rect = self.geometry()
        new_rect = QRect(current_rect.x(), current_rect.y() - 5, 
                        current_rect.width(), current_rect.height())
        
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()
        
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """عند خروج الماوس"""
        current_rect = self.geometry()
        new_rect = QRect(current_rect.x(), current_rect.y() + 5, 
                        current_rect.width(), current_rect.height())
        
        self.animation.setStartValue(current_rect)
        self.animation.setEndValue(new_rect)
        self.animation.start()
        
        super().leaveEvent(event)

class ModernSidebar(QFrame):
    """قائمة جانبية عصرية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedWidth(280)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة القائمة الجانبية"""
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:0.5 #34495e, stop:1 #2c3e50);
                border-top-right-radius: 25px;
                border-bottom-right-radius: 25px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 25, 0, 25)
        layout.setSpacing(10)
        
        # عنوان التطبيق المحسن
        self.create_header(layout)
        
        # أزرار القائمة
        self.create_menu_buttons(layout)
        
        layout.addStretch()
        
        # معلومات الإصدار
        self.create_footer(layout)
        
    def create_header(self, layout):
        """إنشاء رأس القائمة"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 20px;
                margin: 15px;
                padding: 20px;
            }
        """)
        
        # إضافة ظل للرأس
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        header_frame.setGraphicsEffect(shadow)
        
        header_layout = QVBoxLayout(header_frame)
        
        # أيقونة التطبيق
        app_icon = QLabel("💰")
        app_icon.setAlignment(Qt.AlignCenter)
        app_icon.setStyleSheet("font-size: 40px; margin-bottom: 10px;")
        header_layout.addWidget(app_icon)
        
        # عنوان التطبيق
        title = QLabel("مدير المصاريف")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        header_layout.addWidget(title)
        
        # العنوان الفرعي
        subtitle = QLabel("الإدارة الذكية للأموال")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
                font-style: italic;
            }
        """)
        header_layout.addWidget(subtitle)
        
        layout.addWidget(header_frame)
        
    def create_menu_buttons(self, layout):
        """إنشاء أزرار القائمة"""
        buttons_data = [
            ("🏠", "الرئيسية", "#3498db"),
            ("💳", "المصاريف", "#e74c3c"),
            ("📊", "الديون", "#f39c12"),
            ("📈", "التقارير", "#9b59b6"),
            ("⚙️", "الإعدادات", "#34495e")
        ]
        
        for icon, text, color in buttons_data:
            btn = self.create_menu_button(icon, text, color)
            layout.addWidget(btn)
            
    def create_menu_button(self, icon, text, color):
        """إنشاء زر قائمة محسن"""
        btn = QPushButton(f"{icon}  {text}")
        btn.setCheckable(True)
        btn.setCursor(Qt.PointingHandCursor)
        
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: #ecf0f1;
                text-align: left;
                padding: 20px 30px;
                border: none;
                border-radius: 15px;
                font-size: 16px;
                font-weight: 500;
                margin: 3px 15px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}40, stop:1 {color}20);
                color: white;
                border-left: 4px solid {color};
            }}
            QPushButton:checked {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {color}, stop:1 {color}80);
                color: white;
                font-weight: bold;
                border-left: 5px solid #f39c12;
            }}
        """)
        
        return btn
        
    def create_footer(self, layout):
        """إنشاء تذييل القائمة"""
        footer_frame = QFrame()
        footer_frame.setStyleSheet("""
            QFrame {
                background: rgba(52, 73, 94, 0.4);
                border-radius: 15px;
                margin: 15px;
                padding: 20px;
            }
        """)
        
        footer_layout = QVBoxLayout(footer_frame)
        
        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 8px;
            }
        """)
        footer_layout.addWidget(version_label)
        
        developer_label = QLabel("تطوير: Augment Agent")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                font-style: italic;
            }
        """)
        footer_layout.addWidget(developer_label)
        
        layout.addWidget(footer_frame)
