"""
ملف الأنماط والتصميم للتطبيق
يحتوي على جميع أنماط CSS المستخدمة في التطبيق
"""

# الألوان الأساسية
COLORS = {
    'primary': '#3498db',
    'primary_dark': '#2980b9',
    'primary_light': '#5dade2',
    'secondary': '#2c3e50',
    'secondary_dark': '#34495e',
    'success': '#27ae60',
    'success_light': '#2ecc71',
    'danger': '#e74c3c',
    'danger_light': '#ec7063',
    'warning': '#f39c12',
    'warning_light': '#f4d03f',
    'info': '#8e44ad',
    'info_light': '#bb8fce',
    'light': '#ecf0f1',
    'dark': '#2c3e50',
    'white': '#ffffff',
    'gray_light': '#f8f9fa',
    'gray': '#7f8c8d',
    'gray_dark': '#95a5a6',
    'border': '#e9ecef'
}

# أنماط النافذة الرئيسية
MAIN_WINDOW_STYLE = f"""
QMainWindow {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
        stop:0 {COLORS['gray_light']}, stop:1 {COLORS['border']});
}}
"""

# أنماط القائمة الجانبية
SIDEBAR_STYLE = f"""
QFrame#sidebar {{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 {COLORS['secondary']}, stop:1 {COLORS['secondary_dark']});
    border-radius: 0px;
    border-right: 3px solid {COLORS['primary']};
}}

QPushButton#menu_button {{
    background-color: transparent;
    color: {COLORS['light']};
    text-align: left;
    padding: 18px 25px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    margin: 2px 8px;
}}

QPushButton#menu_button:hover {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['secondary_dark']}, stop:1 {COLORS['primary']});
    color: white;
    border-right: 3px solid {COLORS['warning']};
}}

QPushButton#menu_button:checked {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['primary']}, stop:1 {COLORS['primary_light']});
    border-left: 4px solid {COLORS['warning']};
    color: white;
    font-weight: bold;
}}
"""

# أنماط البطاقات
CARD_STYLE = f"""
QFrame.stat_card {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 white, stop:1 {COLORS['gray_light']});
    border-radius: 15px;
    padding: 20px;
    border: 2px solid {COLORS['border']};
    min-height: 120px;
    max-width: 250px;
}}

QFrame.stat_card:hover {{
    border: 3px solid {COLORS['primary']};
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 white, stop:1 #f1f2f6);
}}
"""

# أنماط الأزرار
BUTTON_STYLES = f"""
QPushButton.primary_button {{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 {COLORS['primary']}, stop:1 {COLORS['primary_dark']});
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 13px;
}}

QPushButton.primary_button:hover {{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 {COLORS['primary_light']}, stop:1 {COLORS['primary']});
    border: 2px solid {COLORS['primary_dark']};
}}

QPushButton.primary_button:pressed {{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 {COLORS['primary_dark']}, stop:1 #21618c);
}}

QPushButton.success_button {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['success']}, stop:1 {COLORS['success_light']});
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: bold;
    text-align: left;
}}

QPushButton.success_button:hover {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #229954, stop:1 {COLORS['success']});
}}

QPushButton.danger_button {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['danger']}, stop:1 {COLORS['danger_light']});
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: bold;
    text-align: left;
}}

QPushButton.danger_button:hover {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #c0392b, stop:1 {COLORS['danger']});
}}

QPushButton.info_button {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['primary']}, stop:1 {COLORS['primary_light']});
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: bold;
    text-align: left;
}}

QPushButton.info_button:hover {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['primary_dark']}, stop:1 {COLORS['primary']});
}}
"""

# أنماط الإطارات
FRAME_STYLES = f"""
QFrame.content_frame {{
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 white, stop:1 {COLORS['gray_light']});
    border-radius: 15px;
    padding: 25px;
    border: 2px solid {COLORS['border']};
}}

QFrame.header_frame {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 {COLORS['primary']}, stop:1 {COLORS['primary_dark']});
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 10px;
}}

QFrame.title_frame {{
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 {COLORS['primary']}, stop:1 {COLORS['primary_dark']});
    border-radius: 12px;
    margin: 10px;
    padding: 15px;
}}

QFrame.version_frame {{
    background-color: rgba(52, 73, 94, 0.3);
    border-radius: 8px;
    margin: 10px;
    padding: 15px;
}}
"""

# أنماط النصوص
TEXT_STYLES = f"""
QLabel.page_title {{
    font-size: 22px;
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
}}

QLabel.page_subtitle {{
    font-size: 14px;
    color: {COLORS['light']};
    font-style: italic;
}}

QLabel.section_title {{
    font-size: 18px; 
    font-weight: bold; 
    color: {COLORS['secondary']};
    margin-left: 10px;
}}

QLabel.stat_title {{
    font-size: 14px; 
    color: {COLORS['gray']}; 
    font-weight: 500;
    margin: 5px 0px;
}}

QLabel.stat_value {{
    font-size: 20px; 
    font-weight: bold; 
    margin-bottom: 5px;
}}

QLabel.version_text {{
    color: #bdc3c7;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}}

QLabel.developer_text {{
    color: {COLORS['gray_dark']};
    font-size: 9px;
    font-style: italic;
}}
"""

# أنماط الجداول
TABLE_STYLES = f"""
QTableWidget {{
    background-color: white;
    border-radius: 8px;
    gridline-color: {COLORS['border']};
    border: 1px solid {COLORS['border']};
}}

QTableWidget::item {{
    padding: 10px;
    border-bottom: 1px solid {COLORS['border']};
}}

QTableWidget::item:selected {{
    background-color: {COLORS['primary']};
    color: white;
}}

QHeaderView::section {{
    background-color: {COLORS['secondary_dark']};
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
}}
"""

# دمج جميع الأنماط
ALL_STYLES = f"""
{MAIN_WINDOW_STYLE}
{SIDEBAR_STYLE}
{CARD_STYLE}
{BUTTON_STYLES}
{FRAME_STYLES}
{TEXT_STYLES}
{TABLE_STYLES}

QLabel {{
    color: {COLORS['secondary']};
}}

QFrame {{
    background-color: white;
    border-radius: 12px;
    border: 1px solid {COLORS['border']};
}}
"""

def get_color(color_name):
    """الحصول على لون من قاموس الألوان"""
    return COLORS.get(color_name, COLORS['primary'])

def get_style_for_component(component_name):
    """الحصول على نمط لمكون معين"""
    styles = {
        'main_window': MAIN_WINDOW_STYLE,
        'sidebar': SIDEBAR_STYLE,
        'card': CARD_STYLE,
        'button': BUTTON_STYLES,
        'frame': FRAME_STYLES,
        'text': TEXT_STYLES,
        'table': TABLE_STYLES
    }
    return styles.get(component_name, "")
