import sys
from PyQt5.QtWidgets import (<PERSON>A<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QStackedWidget, QLabel,
                             QFrame, QScrollArea, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from database import DatabaseManager
from expense_widget import ExpenseWidget
from debt_widget import DebtWidget
from reports_widget import ReportsWidget
from datetime import date

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        self.setWindowTitle("مدير المصاريف الشخصية")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # إعداد الألوان
        self.setup_style()
        
        # إنشاء الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # القائمة الجانبية
        self.create_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        self.create_main_content()
        main_layout.addWidget(self.main_content, 1)
        
        # إعداد الصفحات
        self.setup_pages()
        
        # عرض الصفحة الرئيسية
        self.show_dashboard()
        
    def setup_style(self):
        """إعداد الألوان والأنماط"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QFrame {
                background-color: white;
                border-radius: 8px;
            }
        """)
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(250)
        self.sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 0px;
            }
            QPushButton {
                background-color: transparent;
                color: #ecf0f1;
                text-align: left;
                padding: 15px 20px;
                border: none;
                border-radius: 0px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #34495e;
            }
            QPushButton:checked {
                background-color: #3498db;
                border-left: 4px solid #2980b9;
            }
        """)
        
        layout = QVBoxLayout(self.sidebar)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(5)
        
        # عنوان التطبيق
        title = QLabel("مدير المصاريف")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                border-bottom: 1px solid #34495e;
            }
        """)
        layout.addWidget(title)
        
        # أزرار القائمة
        self.menu_buttons = {}
        
        buttons_data = [
            ("dashboard", "🏠 الرئيسية", self.show_dashboard),
            ("expenses", "💰 المصاريف", self.show_expenses),
            ("debts", "📋 الديون", self.show_debts),
            ("reports", "📊 التقارير", self.show_reports),
            ("settings", "⚙️ الإعدادات", self.show_settings)
        ]
        
        for key, text, callback in buttons_data:
            btn = QPushButton(text)
            btn.setCheckable(True)
            btn.clicked.connect(callback)
            self.menu_buttons[key] = btn
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                padding: 10px;
            }
        """)
        layout.addWidget(version_label)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_content = QStackedWidget()
        self.main_content.setStyleSheet("""
            QStackedWidget {
                background-color: #f8f9fa;
                padding: 20px;
            }
        """)
        
    def setup_pages(self):
        """إعداد صفحات التطبيق"""
        # الصفحة الرئيسية
        self.dashboard_widget = self.create_dashboard()
        self.main_content.addWidget(self.dashboard_widget)
        
        # صفحة المصاريف
        self.expense_widget = ExpenseWidget(self.db)
        self.main_content.addWidget(self.expense_widget)
        
        # صفحة الديون
        self.debt_widget = DebtWidget(self.db)
        self.main_content.addWidget(self.debt_widget)
        
        # صفحة التقارير
        self.reports_widget = ReportsWidget(self.db)
        self.main_content.addWidget(self.reports_widget)
        
        # صفحة الإعدادات
        self.settings_widget = self.create_settings()
        self.main_content.addWidget(self.settings_widget)
        
    def create_dashboard(self):
        """إنشاء الصفحة الرئيسية"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title = QLabel("لوحة التحكم")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # بطاقات الإحصائيات
        stats_layout = QHBoxLayout()
        
        # إجمالي المصاريف هذا الشهر
        today = date.today()
        start_date = date(today.year, today.month, 1)
        monthly_expenses = self.db.get_expenses(start_date=start_date)
        monthly_total = sum(expense[1] for expense in monthly_expenses)
        
        stats_layout.addWidget(self.create_stat_card("💰", "مصاريف الشهر", f"{monthly_total:,.2f} ريال"))
        
        # عدد الديون المعلقة
        pending_debts = self.db.get_debts(is_paid=False)
        stats_layout.addWidget(self.create_stat_card("📋", "ديون معلقة", str(len(pending_debts))))
        
        # إجمالي الديون لي
        my_debts = [debt for debt in pending_debts if debt[4] == 'لي']
        my_debts_total = sum(debt[2] for debt in my_debts)
        stats_layout.addWidget(self.create_stat_card("⬆️", "ديون لي", f"{my_debts_total:,.2f} ريال"))
        
        # إجمالي الديون عليّ
        on_me_debts = [debt for debt in pending_debts if debt[4] == 'عليّ']
        on_me_debts_total = sum(debt[2] for debt in on_me_debts)
        stats_layout.addWidget(self.create_stat_card("⬇️", "ديون عليّ", f"{on_me_debts_total:,.2f} ريال"))
        
        layout.addLayout(stats_layout)
        
        # آخر المصاريف
        recent_expenses_frame = QFrame()
        recent_expenses_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        recent_layout = QVBoxLayout(recent_expenses_frame)
        
        recent_title = QLabel("آخر المصاريف")
        recent_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        recent_layout.addWidget(recent_title)
        
        recent_expenses = self.db.get_expenses()[:5]  # آخر 5 مصاريف
        for expense in recent_expenses:
            expense_label = QLabel(f"{expense[2]} - {expense[1]:,.2f} ريال - {expense[3]}")
            expense_label.setStyleSheet("padding: 5px; border-bottom: 1px solid #ecf0f1;")
            recent_layout.addWidget(expense_label)
        
        layout.addWidget(recent_expenses_frame)
        layout.addStretch()
        
        return widget
        
    def create_stat_card(self, icon, title, value):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #3498db;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px; margin-bottom: 10px;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #7f8c8d; margin-bottom: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return card
        
    def create_settings(self):
        """إنشاء صفحة الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title = QLabel("الإعدادات")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        settings_label = QLabel("صفحة الإعدادات قيد التطوير...")
        settings_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(settings_label)
        layout.addStretch()
        
        return widget
        
    def setup_timer(self):
        """إعداد مؤقت لتحديث البيانات"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_dashboard)
        self.timer.start(60000)  # تحديث كل دقيقة
        
    def refresh_dashboard(self):
        """تحديث بيانات الصفحة الرئيسية"""
        if self.main_content.currentWidget() == self.dashboard_widget:
            # إعادة إنشاء الصفحة الرئيسية
            self.main_content.removeWidget(self.dashboard_widget)
            self.dashboard_widget = self.create_dashboard()
            self.main_content.insertWidget(0, self.dashboard_widget)
            self.main_content.setCurrentIndex(0)
    
    def set_active_button(self, active_key):
        """تحديد الزر النشط في القائمة الجانبية"""
        for key, button in self.menu_buttons.items():
            button.setChecked(key == active_key)
    
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.set_active_button("dashboard")
        self.main_content.setCurrentIndex(0)
        self.refresh_dashboard()
    
    def show_expenses(self):
        """عرض صفحة المصاريف"""
        self.set_active_button("expenses")
        self.main_content.setCurrentIndex(1)
        self.expense_widget.refresh_data()
    
    def show_debts(self):
        """عرض صفحة الديون"""
        self.set_active_button("debts")
        self.main_content.setCurrentIndex(2)
        self.debt_widget.refresh_data()
    
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.set_active_button("reports")
        self.main_content.setCurrentIndex(3)
        self.reports_widget.refresh_data()
    
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.set_active_button("settings")
        self.main_content.setCurrentIndex(4)

def main():
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())
