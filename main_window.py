import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QStackedWidget, QLabel,
                             QFrame, QScrollArea, QMessageBox, QGraphicsDropShadowEffect)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QPoint
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from database import DatabaseManager
from expense_widget import ExpenseWidget
from debt_widget import DebtWidget
from reports_widget import ReportsWidget
from ui_components import AnimatedButton, GlowCard, StatCard, ModernSidebar
from visual_effects import AnimationManager, ShadowEffects, apply_modern_theme
from datetime import date

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.animations = []  # لحفظ الأنيميشن
        self.init_ui()
        self.setup_timer()
        self.setup_animations()
        
    def init_ui(self):
        """إعداد واجهة المستخدم الرئيسية المحسنة"""
        self.setWindowTitle("💰 مدير المصاريف الشخصية - الإصدار المحسن")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 700)

        # إعداد الخط العربي المحسن
        font = QFont("Segoe UI", 11)
        font.setWeight(QFont.Medium)
        self.setFont(font)

        # إعداد الألوان والأنماط المحسنة
        self.setup_modern_style()

        # إضافة ظل للنافذة الرئيسية
        self.add_window_shadow()

        # إنشاء الواجهة الرئيسية
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:0.5 #ffffff, stop:1 #e9ecef);
                border-radius: 20px;
            }
        """)
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي مع هوامش محسنة
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # القائمة الجانبية المحسنة
        self.create_modern_sidebar()
        main_layout.addWidget(self.sidebar)

        # المحتوى الرئيسي مع إطار محسن
        self.create_enhanced_main_content()
        main_layout.addWidget(self.main_content, 1)

        # إعداد الصفحات
        self.setup_pages()

        # عرض الصفحة الرئيسية مع أنيميشن
        self.show_dashboard()


    def add_window_shadow(self):
        """إضافة ظل للنافذة الرئيسية"""
        # إضافة ظل للنافذة المركزية بدلاً من النافذة الرئيسية
        central_widget = self.centralWidget()
        if central_widget:
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(25)
            shadow.setColor(QColor(0, 0, 0, 80))
            shadow.setOffset(0, 8)
            central_widget.setGraphicsEffect(shadow)

    def setup_animations(self):
        """إعداد الأنيميشن الأولي"""
        # أنيميشن ظهور النافذة
        self.fade_in_animation = AnimationManager.fade_in(self, 800)
        self.animations.append(self.fade_in_animation)

    def setup_modern_style(self):
        """إعداد الألوان والأنماط المحسنة"""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8f9fa, stop:0.5 #ffffff, stop:1 #e9ecef);
                border-radius: 20px;
            }
            QLabel {
                color: #2c3e50;
                font-weight: 500;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border: none;
                padding: 14px 25px;
                border-radius: 12px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5dade2, stop:1 #3498db);
                border: 2px solid #2980b9;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2980b9, stop:1 #21618c);
            }
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.95), stop:1 rgba(248,249,250,0.95));
                border-radius: 15px;
                border: 2px solid rgba(233, 236, 239, 0.8);
            }
            QStackedWidget {
                background: transparent;
                border-radius: 15px;
            }
        """)

    def create_modern_sidebar(self):
        """إنشاء القائمة الجانبية المحسنة"""
        self.sidebar = ModernSidebar()

        # إضافة ظل للقائمة الجانبية
        ShadowEffects.add_drop_shadow(self.sidebar, blur_radius=25, offset=(5, 0), opacity=60)

        # ربط الأزرار بالوظائف
        self.setup_sidebar_connections()

    def setup_sidebar_connections(self):
        """ربط أزرار القائمة الجانبية بالوظائف"""
        # البحث عن الأزرار في القائمة الجانبية
        buttons = self.sidebar.findChildren(QPushButton)
        self.menu_buttons = {}

        for button in buttons:
            text = button.text()
            if "الرئيسية" in text:
                button.clicked.connect(self.show_dashboard)
                self.menu_buttons["dashboard"] = button
            elif "المصاريف" in text:
                button.clicked.connect(self.show_expenses)
                self.menu_buttons["expenses"] = button
            elif "الديون" in text:
                button.clicked.connect(self.show_debts)
                self.menu_buttons["debts"] = button
            elif "التقارير" in text:
                button.clicked.connect(self.show_reports)
                self.menu_buttons["reports"] = button
            elif "الإعدادات" in text:
                button.clicked.connect(self.show_settings)
                self.menu_buttons["settings"] = button

    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(250)
        self.sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-radius: 0px;
                border-right: 3px solid #3498db;
            }
            QPushButton {
                background-color: transparent;
                color: #ecf0f1;
                text-align: left;
                padding: 18px 25px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
                font-weight: 500;
                margin: 2px 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #34495e, stop:1 #3498db);
                color: white;
                border-right: 3px solid #f39c12;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #5dade2);
                border-left: 4px solid #f39c12;
                color: white;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout(self.sidebar)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(5)
        
        # عنوان التطبيق المحسن
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 12px;
                margin: 10px;
                padding: 15px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)

        app_icon = QLabel("💰")
        app_icon.setAlignment(Qt.AlignCenter)
        app_icon.setStyleSheet("font-size: 32px; margin-bottom: 5px;")
        title_layout.addWidget(app_icon)

        title = QLabel("مدير المصاريف")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        title_layout.addWidget(title)

        subtitle = QLabel("الإدارة الذكية للأموال")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 11px;
                font-style: italic;
            }
        """)
        title_layout.addWidget(subtitle)
        layout.addWidget(title_frame)
        
        # أزرار القائمة
        self.menu_buttons = {}
        
        buttons_data = [
            ("dashboard", "🏠  الرئيسية", self.show_dashboard),
            ("expenses", "💳  المصاريف", self.show_expenses),
            ("debts", "📊  الديون", self.show_debts),
            ("reports", "📈  التقارير", self.show_reports),
            ("settings", "⚙️  الإعدادات", self.show_settings)
        ]
        
        for key, text, callback in buttons_data:
            btn = QPushButton(text)
            btn.setCheckable(True)
            btn.clicked.connect(callback)
            self.menu_buttons[key] = btn
            layout.addWidget(btn)
        
        layout.addStretch()

        # معلومات الإصدار المحسنة
        version_frame = QFrame()
        version_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(52, 73, 94, 0.3);
                border-radius: 8px;
                margin: 10px;
                padding: 15px;
            }
        """)
        version_layout = QVBoxLayout(version_frame)

        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 12px;
                font-weight: bold;
                margin-bottom: 5px;
            }
        """)
        version_layout.addWidget(version_label)

        developer_label = QLabel("تطوير: Augment Agent")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 9px;
                font-style: italic;
            }
        """)
        version_layout.addWidget(developer_label)

        layout.addWidget(version_frame)

    def create_enhanced_main_content(self):
        """إنشاء المحتوى الرئيسي المحسن"""
        self.main_content = QStackedWidget()

        # إضافة ظل للمحتوى الرئيسي
        ShadowEffects.add_drop_shadow(self.main_content, blur_radius=20, offset=(0, 5), opacity=40)

        self.main_content.setStyleSheet("""
            QStackedWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255,255,255,0.98), stop:1 rgba(248,249,250,0.98));
                border-radius: 20px;
                border: 2px solid rgba(52, 152, 219, 0.2);
                margin: 5px;
            }
        """)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_content = QStackedWidget()
        self.main_content.setStyleSheet("""
            QStackedWidget {
                background-color: #f8f9fa;
                padding: 20px;
            }
        """)
        
    def setup_pages(self):
        """إعداد صفحات التطبيق"""
        # الصفحة الرئيسية
        self.dashboard_widget = self.create_dashboard()
        self.main_content.addWidget(self.dashboard_widget)
        
        # صفحة المصاريف
        self.expense_widget = ExpenseWidget(self.db)
        self.main_content.addWidget(self.expense_widget)
        
        # صفحة الديون
        self.debt_widget = DebtWidget(self.db)
        self.main_content.addWidget(self.debt_widget)
        
        # صفحة التقارير
        self.reports_widget = ReportsWidget(self.db)
        self.main_content.addWidget(self.reports_widget)
        
        # صفحة الإعدادات
        self.settings_widget = self.create_settings()
        self.main_content.addWidget(self.settings_widget)
        
    def create_dashboard(self):
        """إنشاء الصفحة الرئيسية المحسنة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(25)

        # عنوان الصفحة المحسن
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                padding: 20px;
                margin-bottom: 10px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)

        welcome_icon = QLabel("👋")
        welcome_icon.setStyleSheet("font-size: 32px;")
        header_layout.addWidget(welcome_icon)

        welcome_text = QVBoxLayout()
        title = QLabel("مرحباً بك في لوحة التحكم")
        title.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: white;
                margin-bottom: 5px;
            }
        """)
        welcome_text.addWidget(title)

        subtitle = QLabel("إدارة ذكية ومتقدمة لأموالك الشخصية")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #ecf0f1;
                font-style: italic;
            }
        """)
        welcome_text.addWidget(subtitle)

        header_layout.addLayout(welcome_text)
        header_layout.addStretch()

        # إضافة التاريخ والوقت
        from datetime import datetime
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        time_label = QLabel(current_time)
        time_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                background-color: rgba(255,255,255,0.2);
                padding: 8px 12px;
                border-radius: 6px;
            }
        """)
        header_layout.addWidget(time_label)

        layout.addWidget(header_frame)
        
        # بطاقات الإحصائيات المحسنة مع أنيميشن
        stats_container = QFrame()
        stats_container.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: none;
            }
        """)
        stats_layout = QHBoxLayout(stats_container)
        stats_layout.setSpacing(25)

        # إجمالي المصاريف هذا الشهر
        today = date.today()
        start_date = date(today.year, today.month, 1)
        monthly_expenses = self.db.get_expenses(start_date=start_date)
        monthly_total = sum(expense[1] for expense in monthly_expenses)

        # إنشاء البطاقات المحسنة
        expense_card = StatCard("💳", "مصاريف الشهر", f"{monthly_total:,.2f} ريال", "#e74c3c")
        stats_layout.addWidget(expense_card)

        # عدد الديون المعلقة
        pending_debts = self.db.get_debts(is_paid=False)
        debt_count_card = StatCard("📊", "ديون معلقة", str(len(pending_debts)), "#f39c12")
        stats_layout.addWidget(debt_count_card)

        # إجمالي الديون لي
        my_debts = [debt for debt in pending_debts if debt[4] == 'لي']
        my_debts_total = sum(debt[2] for debt in my_debts)
        my_debts_card = StatCard("💰", "ديون لي", f"{my_debts_total:,.2f} ريال", "#27ae60")
        stats_layout.addWidget(my_debts_card)

        # إجمالي الديون عليّ
        on_me_debts = [debt for debt in pending_debts if debt[4] == 'عليّ']
        on_me_debts_total = sum(debt[2] for debt in on_me_debts)
        on_me_debts_card = StatCard("📉", "ديون عليّ", f"{on_me_debts_total:,.2f} ريال", "#8e44ad")
        stats_layout.addWidget(on_me_debts_card)

        # إضافة أنيميشن للبطاقات
        cards = [expense_card, debt_count_card, my_debts_card, on_me_debts_card]
        for i, card in enumerate(cards):
            # تأخير مختلف لكل بطاقة
            QTimer.singleShot(i * 200, lambda c=card: AnimationManager.fade_in(c, 600))

        layout.addWidget(stats_container)
        
        # قسم آخر المصاريف والإحصائيات السريعة
        bottom_layout = QHBoxLayout()
        bottom_layout.setSpacing(20)

        # آخر المصاريف المحسن
        recent_expenses_frame = QFrame()
        recent_expenses_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border-radius: 15px;
                padding: 25px;
                border: 2px solid #e9ecef;
            }
        """)
        recent_layout = QVBoxLayout(recent_expenses_frame)

        # عنوان القسم
        recent_header = QHBoxLayout()
        recent_icon = QLabel("📝")
        recent_icon.setStyleSheet("font-size: 24px;")
        recent_header.addWidget(recent_icon)

        recent_title = QLabel("آخر المصاريف")
        recent_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 10px;
        """)
        recent_header.addWidget(recent_title)
        recent_header.addStretch()

        view_all_btn = QPushButton("عرض الكل")
        view_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        view_all_btn.clicked.connect(self.show_expenses)
        recent_header.addWidget(view_all_btn)

        recent_layout.addLayout(recent_header)

        # قائمة المصاريف
        recent_expenses = self.db.get_expenses()[:5]  # آخر 5 مصاريف
        if recent_expenses:
            for i, expense in enumerate(recent_expenses):
                expense_frame = QFrame()
                expense_frame.setStyleSheet("""
                    QFrame {
                        background-color: #f8f9fa;
                        border-radius: 8px;
                        padding: 12px;
                        margin: 5px 0px;
                        border-left: 4px solid #3498db;
                    }
                """)
                expense_layout = QHBoxLayout(expense_frame)

                # أيقونة المصروف
                expense_icon = QLabel("💳")
                expense_icon.setStyleSheet("font-size: 16px;")
                expense_layout.addWidget(expense_icon)

                # تفاصيل المصروف
                expense_details = QVBoxLayout()
                expense_desc = QLabel(expense[2])
                expense_desc.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 13px;")
                expense_details.addWidget(expense_desc)

                expense_info = QLabel(f"{expense[4] or 'غير محدد'} • {expense[3]}")
                expense_info.setStyleSheet("color: #7f8c8d; font-size: 11px;")
                expense_details.addWidget(expense_info)

                expense_layout.addLayout(expense_details)
                expense_layout.addStretch()

                # المبلغ
                amount_label = QLabel(f"{expense[1]:,.2f} ريال")
                amount_label.setStyleSheet("""
                    color: #e74c3c;
                    font-weight: bold;
                    font-size: 14px;
                    background-color: white;
                    padding: 5px 10px;
                    border-radius: 5px;
                """)
                expense_layout.addWidget(amount_label)

                recent_layout.addWidget(expense_frame)
        else:
            no_data_label = QLabel("لا توجد مصاريف مسجلة")
            no_data_label.setAlignment(Qt.AlignCenter)
            no_data_label.setStyleSheet("""
                color: #7f8c8d;
                font-style: italic;
                padding: 30px;
                font-size: 14px;
            """)
            recent_layout.addWidget(no_data_label)

        bottom_layout.addWidget(recent_expenses_frame, 2)

        # قسم الإجراءات السريعة
        quick_actions_frame = QFrame()
        quick_actions_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border-radius: 15px;
                padding: 25px;
                border: 2px solid #e9ecef;
            }
        """)
        quick_layout = QVBoxLayout(quick_actions_frame)

        # عنوان القسم
        quick_header = QHBoxLayout()
        quick_icon = QLabel("⚡")
        quick_icon.setStyleSheet("font-size: 24px;")
        quick_header.addWidget(quick_icon)

        quick_title = QLabel("إجراءات سريعة")
        quick_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-left: 10px;
        """)
        quick_header.addWidget(quick_title)
        quick_header.addStretch()
        quick_layout.addLayout(quick_header)

        # أزرار الإجراءات السريعة
        quick_buttons_layout = QVBoxLayout()
        quick_buttons_layout.setSpacing(15)

        # أزرار محسنة مع تأثيرات
        buttons_data = [
            ("💳  إضافة مصروف جديد", "#27ae60", self.show_expenses),
            ("📊  إضافة دين جديد", "#e74c3c", self.show_debts),
            ("📈  عرض التقارير", "#3498db", self.show_reports)
        ]

        for i, (text, color, callback) in enumerate(buttons_data):
            btn = AnimatedButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {color}, stop:1 {color}80);
                    color: white;
                    border: none;
                    padding: 18px 25px;
                    border-radius: 12px;
                    font-size: 15px;
                    font-weight: bold;
                    text-align: left;
                    min-height: 25px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {color}E0, stop:1 {color});
                    border: 2px solid {color}40;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {color}80, stop:1 {color}60);
                }}
            """)
            btn.clicked.connect(callback)

            # إضافة ظل للزر
            ShadowEffects.add_drop_shadow(btn, blur_radius=15, offset=(0, 3), opacity=60)

            # أنيميشن ظهور متدرج
            QTimer.singleShot(i * 150, lambda b=btn: AnimationManager.slide_in_from_right(b, 400))

            quick_buttons_layout.addWidget(btn)

        quick_layout.addLayout(quick_buttons_layout)
        quick_layout.addStretch()

        bottom_layout.addWidget(quick_actions_frame, 1)

        layout.addLayout(bottom_layout)
        layout.addStretch()

        return widget

    def create_enhanced_stat_card(self, icon, title, value, color, trend_icon):
        """إنشاء بطاقة إحصائية محسنة"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 #f8f9fa);
                border-radius: 15px;
                padding: 20px;
                border: 2px solid #e9ecef;
                min-height: 120px;
                max-width: 250px;
            }}
            QFrame:hover {{
                border: 3px solid {color};
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 white, stop:1 #f1f2f6);
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(10)

        # الصف العلوي - الأيقونة والاتجاه
        top_row = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            font-size: 28px;
            color: {color};
            background-color: {color}20;
            padding: 10px;
            border-radius: 10px;
            min-width: 50px;
            max-width: 50px;
            text-align: center;
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        top_row.addWidget(icon_label)

        top_row.addStretch()

        trend_label = QLabel(trend_icon)
        trend_label.setStyleSheet(f"""
            font-size: 20px;
            color: {color};
            background-color: {color}15;
            padding: 5px;
            border-radius: 5px;
        """)
        top_row.addWidget(trend_label)

        layout.addLayout(top_row)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
            margin: 5px 0px;
        """)
        layout.addWidget(title_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {color};
            margin-bottom: 5px;
        """)
        layout.addWidget(value_label)

        layout.addStretch()

        return card

    def create_stat_card(self, icon, title, value):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #3498db;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("font-size: 24px; margin-bottom: 10px;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #7f8c8d; margin-bottom: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return card
        
    def create_settings(self):
        """إنشاء صفحة الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title = QLabel("الإعدادات")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        settings_label = QLabel("صفحة الإعدادات قيد التطوير...")
        settings_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(settings_label)
        layout.addStretch()
        
        return widget
        
    def setup_timer(self):
        """إعداد مؤقت لتحديث البيانات"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_dashboard)
        self.timer.start(60000)  # تحديث كل دقيقة
        
    def refresh_dashboard(self):
        """تحديث بيانات الصفحة الرئيسية"""
        if self.main_content.currentWidget() == self.dashboard_widget:
            # إعادة إنشاء الصفحة الرئيسية
            self.main_content.removeWidget(self.dashboard_widget)
            self.dashboard_widget = self.create_dashboard()
            self.main_content.insertWidget(0, self.dashboard_widget)
            self.main_content.setCurrentIndex(0)
    
    def set_active_button(self, active_key):
        """تحديد الزر النشط في القائمة الجانبية"""
        for key, button in self.menu_buttons.items():
            button.setChecked(key == active_key)
    
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.set_active_button("dashboard")
        self.main_content.setCurrentIndex(0)
        self.refresh_dashboard()
    
    def show_expenses(self):
        """عرض صفحة المصاريف"""
        self.set_active_button("expenses")
        self.main_content.setCurrentIndex(1)
        self.expense_widget.refresh_data()
    
    def show_debts(self):
        """عرض صفحة الديون"""
        self.set_active_button("debts")
        self.main_content.setCurrentIndex(2)
        self.debt_widget.refresh_data()
    
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.set_active_button("reports")
        self.main_content.setCurrentIndex(3)
        self.reports_widget.refresh_data()
    
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.set_active_button("settings")
        self.main_content.setCurrentIndex(4)

def main():
    app = QApplication(sys.argv)
    
    # إعداد الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())
