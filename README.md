# مدير المصاريف الشخصية والديون

تطبيق سطح مكتب شامل لإدارة المصاريف الشخصية والديون باللغة العربية، مطور باستخدام Python و PyQt5.

## الميزات الرئيسية

### 📊 إدارة المصاريف
- تسجيل المصاريف اليومية مع التصنيف
- فئات مصاريف قابلة للتخصيص (طعام، مواصلات، فواتير، إلخ)
- فلترة وبحث متقدم في المصاريف
- عرض المصاريف في جداول منظمة

### 💰 إدارة الديون
- تتبع الديون (مَن تدين له ومَن يدين لك)
- تحديد تواريخ استحقاق للديون
- تذكيرات للديون المستحقة
- تسديد الديون وتتبع حالة السداد

### 📈 التقارير والإحصائيات
- رسوم بيانية تفاعلية للمصاريف
- تحليل الاتجاهات الشهرية والسنوية
- ملخص شامل للوضع المالي
- تصدير التقارير إلى ملفات نصية

### 🔔 نظام التذكيرات
- تذكيرات للديون المستحقة قريباً
- تنبيهات للديون المتأخرة
- عرض الديون المستحقة في الصفحة الرئيسية

## متطلبات النظام

- نظام التشغيل: Windows 10 أو أحدث
- Python 3.7 أو أحدث
- المكتبات المطلوبة (انظر requirements.txt)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd expense-manager
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python main.py
```

## هيكل المشروع

```
expense-manager/
├── main.py                 # الملف الرئيسي لتشغيل التطبيق
├── main_window.py          # النافذة الرئيسية والتنقل
├── database.py             # إدارة قاعدة البيانات SQLite
├── models.py               # نماذج البيانات والتحقق
├── expense_widget.py       # واجهة إدارة المصاريف
├── debt_widget.py          # واجهة إدارة الديون
├── reports_widget.py       # واجهة التقارير والإحصائيات
├── requirements.txt        # المكتبات المطلوبة
├── README.md              # ملف التوثيق
├── data/                  # مجلد البيانات
├── exports/               # مجلد التصدير
└── backups/               # مجلد النسخ الاحتياطية
```

## استخدام التطبيق

### الصفحة الرئيسية
- عرض ملخص سريع للمصاريف والديون
- إحصائيات الشهر الحالي
- آخر المصاريف المسجلة

### إدارة المصاريف
1. انقر على "المصاريف" في القائمة الجانبية
2. استخدم زر "إضافة مصروف" لتسجيل مصروف جديد
3. املأ البيانات المطلوبة (المبلغ، الوصف، الفئة، التاريخ)
4. يمكنك فلترة المصاريف حسب الفئة أو التاريخ

### إدارة الديون
1. انقر على "الديون" في القائمة الجانبية
2. استخدم زر "إضافة دين" لتسجيل دين جديد
3. حدد نوع الدين (لي أو عليّ)
4. يمكنك تحديد تاريخ استحقاق اختياري
5. استخدم زر "تسديد" لتحديد الدين كمسدد

### التقارير والإحصائيات
1. انقر على "التقارير" في القائمة الجانبية
2. اختر الفترة الزمنية المطلوبة
3. استعرض الرسوم البيانية والإحصائيات
4. استخدم زر "تصدير" لحفظ التقرير

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- **categories**: فئات المصاريف
- **expenses**: المصاريف المسجلة
- **debts**: الديون
- **debt_payments**: دفعات الديون

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي بيانات عبر الإنترنت
- يُنصح بعمل نسخ احتياطية دورية من ملف قاعدة البيانات

## المساهمة في التطوير

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة المطلوبة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الدعم الفني

في حالة مواجهة أي مشاكل أو أسئلة:

1. تحقق من ملف README.md
2. راجع متطلبات النظام
3. تأكد من تثبيت جميع المكتبات المطلوبة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الإصدارات

### الإصدار 1.0
- إدارة المصاريف الأساسية
- إدارة الديون
- التقارير والإحصائيات
- واجهة مستخدم باللغة العربية

## خطط التطوير المستقبلية

- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تصدير البيانات إلى Excel
- [ ] إضافة المزيد من أنواع الرسوم البيانية
- [ ] نظام تذكيرات متقدم
- [ ] دعم العملات المتعددة
- [ ] تطبيق الهاتف المحمول

---

**تم تطوير هذا التطبيق بواسطة Augment Agent**
