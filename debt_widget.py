from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                             QTableWidget, QTableWidgetItem, QHeaderView, QLabel,
                             QLineEdit, QComboBox, QDateEdit, QDialog, QFormLayout,
                             QDialogButtonBox, QMessageBox, QFrame, QTabWidget,
                             QDoubleSpinBox, QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import date, datetime
from models import Debt, DebtValidator

class AddDebtDialog(QDialog):
    """نافذة إضافة دين جديد"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إضافة دين جديد")
        self.setModal(True)
        self.resize(400, 350)
        
        layout = QVBoxLayout(self)
        
        # نموذج الإدخال
        form_layout = QFormLayout()
        
        # اسم الشخص
        self.person_input = QLineEdit()
        self.person_input.setPlaceholderText("اسم الشخص...")
        form_layout.addRow("اسم الشخص:", self.person_input)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ريال")
        form_layout.addRow("المبلغ:", self.amount_input)
        
        # نوع الدين
        self.debt_type_combo = QComboBox()
        self.debt_type_combo.addItem("لي (يدين لي)", "لي")
        self.debt_type_combo.addItem("عليّ (أدين له)", "عليّ")
        form_layout.addRow("نوع الدين:", self.debt_type_combo)
        
        # الوصف
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف الدين أو سبب الاستدانة...")
        form_layout.addRow("الوصف:", self.description_input)
        
        # تاريخ الاستحقاق
        self.has_due_date = QCheckBox("تحديد تاريخ استحقاق")
        self.has_due_date.toggled.connect(self.toggle_due_date)
        form_layout.addRow("", self.has_due_date)
        
        self.due_date_input = QDateEdit()
        self.due_date_input.setDate(QDate.currentDate().addDays(30))
        self.due_date_input.setCalendarPopup(True)
        self.due_date_input.setEnabled(False)
        form_layout.addRow("تاريخ الاستحقاق:", self.due_date_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        buttons.button(QDialogButtonBox.Save).setText("حفظ")
        buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
        buttons.accepted.connect(self.save_debt)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
    def toggle_due_date(self, checked):
        """تفعيل/إلغاء تفعيل تاريخ الاستحقاق"""
        self.due_date_input.setEnabled(checked)
        
    def save_debt(self):
        """حفظ الدين الجديد"""
        # جمع البيانات
        person_name = self.person_input.text().strip()
        amount = self.amount_input.value()
        debt_type = self.debt_type_combo.currentData()
        description = self.description_input.toPlainText().strip()
        due_date = None
        
        if self.has_due_date.isChecked():
            due_date = self.due_date_input.date().toPyDate()
        
        # إنشاء كائن الدين للتحقق
        debt = Debt(
            person_name=person_name,
            amount=amount,
            debt_type=debt_type,
            description=description,
            due_date=due_date
        )
        
        # التحقق من صحة البيانات
        errors = DebtValidator.validate_debt(debt)
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors))
            return
        
        try:
            # حفظ في قاعدة البيانات
            self.db.add_debt(person_name, amount, description, debt_type, due_date)
            QMessageBox.information(self, "نجح", "تم إضافة الدين بنجاح!")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الدين:\n{str(e)}")

class DebtWidget(QWidget):
    """واجهة إدارة الديون"""
    
    def __init__(self, db):
        super().__init__()
        self.db = db
        self.init_ui()
        self.load_debts()
        
    def init_ui(self):
        """إعداد واجهة الديون"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title = QLabel("إدارة الديون")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة دين جديد
        add_btn = QPushButton("➕ إضافة دين")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        add_btn.clicked.connect(self.add_debt)
        toolbar_layout.addWidget(add_btn)
        
        toolbar_layout.addStretch()
        
        # فلتر نوع الدين
        type_label = QLabel("النوع:")
        toolbar_layout.addWidget(type_label)
        
        self.type_filter = QComboBox()
        self.type_filter.addItem("جميع الديون", None)
        self.type_filter.addItem("ديون لي", "لي")
        self.type_filter.addItem("ديون عليّ", "عليّ")
        self.type_filter.currentTextChanged.connect(self.filter_debts)
        toolbar_layout.addWidget(self.type_filter)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        toolbar_layout.addWidget(status_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItem("جميع الحالات", None)
        self.status_filter.addItem("غير مسددة", False)
        self.status_filter.addItem("مسددة", True)
        self.status_filter.currentTextChanged.connect(self.filter_debts)
        toolbar_layout.addWidget(self.status_filter)
        
        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        toolbar_layout.addWidget(refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # جدول الديون
        self.debts_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.debts_table)
        
        # شريط الإحصائيات
        self.create_stats_bar()
        layout.addWidget(self.stats_frame)
        
    def create_stats_bar(self):
        """إنشاء شريط الإحصائيات"""
        self.stats_frame = QFrame()
        self.stats_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 15px;
                border-left: 4px solid #e74c3c;
            }
        """)
        stats_layout = QHBoxLayout(self.stats_frame)
        
        self.my_debts_label = QLabel("ديون لي: 0.00 ريال")
        self.my_debts_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #27ae60;")
        stats_layout.addWidget(self.my_debts_label)
        
        stats_layout.addStretch()
        
        self.on_me_debts_label = QLabel("ديون عليّ: 0.00 ريال")
        self.on_me_debts_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #e74c3c;")
        stats_layout.addWidget(self.on_me_debts_label)
        
        stats_layout.addStretch()
        
        self.count_label = QLabel("عدد الديون: 0")
        self.count_label.setStyleSheet("font-size: 14px; color: #7f8c8d;")
        stats_layout.addWidget(self.count_label)
        
    def setup_table(self):
        """إعداد جدول الديون"""
        headers = ["المعرف", "الشخص", "المبلغ", "النوع", "الوصف", "تاريخ الاستحقاق", "الحالة", "الإجراءات"]
        self.debts_table.setColumnCount(len(headers))
        self.debts_table.setHorizontalHeaderLabels(headers)
        
        # إعداد عرض الأعمدة
        header = self.debts_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # المعرف
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الشخص
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(4, QHeaderView.Stretch)           # الوصف
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # تاريخ الاستحقاق
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الإجراءات
        
        # إخفاء عمود المعرف
        self.debts_table.setColumnHidden(0, True)
        
        # إعداد الأنماط
        self.debts_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border-radius: 8px;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #e74c3c;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.debts_table.setAlternatingRowColors(True)
        self.debts_table.setSelectionBehavior(QTableWidget.SelectRows)

    def load_debts(self):
        """تحميل الديون في الجدول"""
        # الحصول على فلاتر البحث
        debt_type = self.type_filter.currentData()
        is_paid = self.status_filter.currentData()

        # جلب الديون من قاعدة البيانات
        debts = self.db.get_debts(debt_type, is_paid)

        # تحديث الجدول
        self.debts_table.setRowCount(len(debts))

        my_debts_total = 0
        on_me_debts_total = 0

        for row, debt in enumerate(debts):
            debt_id, person_name, amount, description, debt_type, due_date, is_paid, created_at = debt

            # حساب الإجماليات
            if not is_paid:
                if debt_type == "لي":
                    my_debts_total += amount
                else:
                    on_me_debts_total += amount

            # إضافة البيانات للجدول
            self.debts_table.setItem(row, 0, QTableWidgetItem(str(debt_id)))
            self.debts_table.setItem(row, 1, QTableWidgetItem(person_name))
            self.debts_table.setItem(row, 2, QTableWidgetItem(f"{amount:,.2f} ريال"))

            # تلوين نوع الدين
            type_item = QTableWidgetItem(debt_type)
            if debt_type == "لي":
                type_item.setBackground(QColor("#d5f4e6"))  # أخضر فاتح
            else:
                type_item.setBackground(QColor("#fadbd8"))  # أحمر فاتح
            self.debts_table.setItem(row, 3, type_item)

            self.debts_table.setItem(row, 4, QTableWidgetItem(description or ""))
            self.debts_table.setItem(row, 5, QTableWidgetItem(due_date or "غير محدد"))

            # حالة الدين
            status_text = "مسدد ✅" if is_paid else "غير مسدد ❌"
            status_item = QTableWidgetItem(status_text)
            if is_paid:
                status_item.setBackground(QColor("#d5f4e6"))
            else:
                # التحقق من انتهاء الموعد
                if due_date:
                    try:
                        due_date_obj = datetime.strptime(due_date, "%Y-%m-%d").date()
                        if due_date_obj < date.today():
                            status_item.setBackground(QColor("#fadbd8"))
                            status_text += " (متأخر)"
                            status_item.setText(status_text)
                    except:
                        pass
            self.debts_table.setItem(row, 6, status_item)

            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)

            if not is_paid:
                # زر تسديد
                pay_btn = QPushButton("💰 تسديد")
                pay_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #27ae60;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 3px;
                        font-size: 11px;
                    }
                    QPushButton:hover {
                        background-color: #229954;
                    }
                """)
                pay_btn.clicked.connect(lambda checked, did=debt_id: self.mark_debt_paid(did))
                actions_layout.addWidget(pay_btn)

            # زر حذف
            delete_btn = QPushButton("🗑️")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 8px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            delete_btn.clicked.connect(lambda checked, did=debt_id: self.delete_debt(did))
            actions_layout.addWidget(delete_btn)

            self.debts_table.setCellWidget(row, 7, actions_widget)

        # تحديث الإحصائيات
        self.update_stats(my_debts_total, on_me_debts_total, len(debts))

    def update_stats(self, my_debts_total, on_me_debts_total, count):
        """تحديث شريط الإحصائيات"""
        self.my_debts_label.setText(f"ديون لي: {my_debts_total:,.2f} ريال")
        self.on_me_debts_label.setText(f"ديون عليّ: {on_me_debts_total:,.2f} ريال")
        self.count_label.setText(f"عدد الديون: {count}")

    def add_debt(self):
        """فتح نافذة إضافة دين جديد"""
        dialog = AddDebtDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()

    def mark_debt_paid(self, debt_id):
        """تحديد دين كمسدد"""
        reply = QMessageBox.question(
            self, "تأكيد التسديد",
            "هل أنت متأكد من تسديد هذا الدين؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.db.mark_debt_paid(debt_id)
                QMessageBox.information(self, "نجح", "تم تسديد الدين بنجاح!")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تسديد الدين:\n{str(e)}")

    def delete_debt(self, debt_id):
        """حذف دين"""
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الدين؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف الدين من قاعدة البيانات
                conn = self.db.get_connection()
                cursor = conn.cursor()
                cursor.execute('DELETE FROM debts WHERE id = ?', (debt_id,))
                conn.commit()
                conn.close()

                QMessageBox.information(self, "نجح", "تم حذف الدين بنجاح!")
                self.refresh_data()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الدين:\n{str(e)}")

    def filter_debts(self):
        """تطبيق فلاتر البحث"""
        self.load_debts()

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_debts()
