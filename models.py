from datetime import datetime, date
from dataclasses import dataclass
from typing import Optional

@dataclass
class Category:
    """نموذج فئة المصاريف"""
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    color: str = "#3498db"
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Expense:
    """نموذج المصروف"""
    id: Optional[int] = None
    amount: float = 0.0
    description: str = ""
    category_id: Optional[int] = None
    date: date = None
    created_at: Optional[datetime] = None
    category_name: Optional[str] = None
    category_color: Optional[str] = None
    
    def __post_init__(self):
        if self.date is None:
            self.date = date.today()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Debt:
    """نموذج الدين"""
    id: Optional[int] = None
    person_name: str = ""
    amount: float = 0.0
    description: str = ""
    debt_type: str = "لي"  # "لي" أو "عليّ"
    due_date: Optional[date] = None
    is_paid: bool = False
    created_at: Optional[datetime] = None
    paid_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    @property
    def is_overdue(self) -> bool:
        """التحقق من انتهاء موعد الدين"""
        if self.due_date and not self.is_paid:
            return date.today() > self.due_date
        return False
    
    @property
    def days_until_due(self) -> Optional[int]:
        """عدد الأيام المتبقية حتى موعد الاستحقاق"""
        if self.due_date and not self.is_paid:
            delta = self.due_date - date.today()
            return delta.days
        return None

@dataclass
class DebtPayment:
    """نموذج دفعة الدين"""
    id: Optional[int] = None
    debt_id: int = 0
    amount: float = 0.0
    payment_date: date = None
    notes: str = ""
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.payment_date is None:
            self.payment_date = date.today()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class ExpenseSummary:
    """نموذج ملخص المصاريف"""
    category_name: str
    category_color: str
    total_amount: float
    expense_count: int
    
    @property
    def average_amount(self) -> float:
        """متوسط المبلغ لكل مصروف"""
        if self.expense_count > 0:
            return self.total_amount / self.expense_count
        return 0.0

class ExpenseValidator:
    """فئة للتحقق من صحة البيانات"""
    
    @staticmethod
    def validate_expense(expense: Expense) -> list:
        """التحقق من صحة بيانات المصروف"""
        errors = []
        
        if expense.amount <= 0:
            errors.append("المبلغ يجب أن يكون أكبر من صفر")
        
        if not expense.description.strip():
            errors.append("وصف المصروف مطلوب")
        
        if expense.category_id is None:
            errors.append("فئة المصروف مطلوبة")
        
        return errors

class DebtValidator:
    """فئة للتحقق من صحة بيانات الديون"""

    @staticmethod
    def validate_debt(debt: Debt) -> list:
        """التحقق من صحة بيانات الدين"""
        errors = []

        if not debt.person_name.strip():
            errors.append("اسم الشخص مطلوب")

        if debt.amount <= 0:
            errors.append("المبلغ يجب أن يكون أكبر من صفر")

        if debt.debt_type not in ["لي", "عليّ"]:
            errors.append("نوع الدين يجب أن يكون 'لي' أو 'عليّ'")

        if debt.due_date and debt.due_date < date.today():
            errors.append("تاريخ الاستحقاق لا يمكن أن يكون في الماضي")

        return errors

class CategoryValidator:
    """فئة للتحقق من صحة بيانات الفئات"""

    @staticmethod
    def validate_category(category: Category) -> list:
        """التحقق من صحة بيانات الفئة"""
        errors = []
        
        if not category.name.strip():
            errors.append("اسم الفئة مطلوب")
        
        if len(category.name) > 50:
            errors.append("اسم الفئة يجب أن يكون أقل من 50 حرف")
        
        # التحقق من صحة كود اللون
        if not category.color.startswith('#') or len(category.color) != 7:
            errors.append("كود اللون غير صحيح")
        
        return errors

class DateHelper:
    """فئة مساعدة للتعامل مع التواريخ"""
    
    @staticmethod
    def get_month_range(year: int, month: int) -> tuple:
        """الحصول على بداية ونهاية الشهر"""
        from calendar import monthrange
        start_date = date(year, month, 1)
        last_day = monthrange(year, month)[1]
        end_date = date(year, month, last_day)
        return start_date, end_date
    
    @staticmethod
    def get_year_range(year: int) -> tuple:
        """الحصول على بداية ونهاية السنة"""
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        return start_date, end_date
    
    @staticmethod
    def format_date_arabic(date_obj: date) -> str:
        """تنسيق التاريخ بالعربية"""
        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        return f"{date_obj.day} {months[date_obj.month - 1]} {date_obj.year}"
    
    @staticmethod
    def get_days_in_arabic(days: int) -> str:
        """تحويل عدد الأيام إلى نص عربي"""
        if days == 0:
            return "اليوم"
        elif days == 1:
            return "غداً"
        elif days == -1:
            return "أمس"
        elif days > 1:
            return f"خلال {days} أيام"
        else:
            return f"منذ {abs(days)} أيام"

class CurrencyHelper:
    """فئة مساعدة للتعامل مع العملة"""
    
    @staticmethod
    def format_currency(amount: float, currency: str = "ريال") -> str:
        """تنسيق المبلغ مع العملة"""
        return f"{amount:,.2f} {currency}"
    
    @staticmethod
    def parse_currency(amount_str: str) -> float:
        """تحويل النص إلى مبلغ"""
        try:
            # إزالة الفواصل والعملة
            clean_amount = amount_str.replace(',', '').replace('ريال', '').strip()
            return float(clean_amount)
        except ValueError:
            return 0.0
