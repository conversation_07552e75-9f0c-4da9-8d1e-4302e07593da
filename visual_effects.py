"""
تأثيرات بصرية متقدمة للتطبيق
يحتوي على أنيميشن وتأثيرات بصرية محسنة
"""

from PyQt5.QtWidgets import QGraphicsDropShadowEffect, QGraphicsOpacityEffect
from PyQt5.QtCore import QPropertyAnimation, QEasingCurve, QParallelAnimationGroup, QSequentialAnimationGroup, QTimer, QPoint
from PyQt5.QtGui import QColor

class AnimationManager:
    """مدير الأنيميشن والتأثيرات البصرية"""
    
    @staticmethod
    def fade_in(widget, duration=500):
        """تأثير الظهور التدريجي"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(0)
        animation.setEndValue(1)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        animation.start()
        
        return animation
    
    @staticmethod
    def fade_out(widget, duration=300):
        """تأثير الاختفاء التدريجي"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(1)
        animation.setEndValue(0)
        animation.setEasingCurve(QEasingCurve.InCubic)
        animation.start()
        
        return animation
    
    @staticmethod
    def slide_in_from_left(widget, duration=600):
        """انزلاق من اليسار"""
        start_pos = widget.pos()
        widget.move(start_pos.x() - widget.width(), start_pos.y())
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(widget.pos())
        animation.setEndValue(start_pos)
        animation.setEasingCurve(QEasingCurve.OutBack)
        animation.start()
        
        return animation
    
    @staticmethod
    def slide_in_from_right(widget, duration=600):
        """انزلاق من اليمين"""
        start_pos = widget.pos()
        widget.move(start_pos.x() + widget.width(), start_pos.y())
        
        animation = QPropertyAnimation(widget, b"pos")
        animation.setDuration(duration)
        animation.setStartValue(widget.pos())
        animation.setEndValue(start_pos)
        animation.setEasingCurve(QEasingCurve.OutBack)
        animation.start()
        
        return animation
    
    @staticmethod
    def bounce_in(widget, duration=800):
        """تأثير الارتداد"""
        original_size = widget.size()
        widget.resize(0, 0)
        
        animation = QPropertyAnimation(widget, b"size")
        animation.setDuration(duration)
        animation.setStartValue(widget.size())
        animation.setEndValue(original_size)
        animation.setEasingCurve(QEasingCurve.OutBounce)
        animation.start()
        
        return animation
    
    @staticmethod
    def pulse_effect(widget, duration=1000):
        """تأثير النبض"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(1)
        animation.setEndValue(0.5)
        animation.setEasingCurve(QEasingCurve.InOutSine)
        animation.setLoopCount(-1)  # تكرار لا نهائي
        animation.start()
        
        return animation

class ShadowEffects:
    """تأثيرات الظلال"""
    
    @staticmethod
    def add_glow_shadow(widget, color="#3498db", blur_radius=20, offset=(0, 0)):
        """إضافة ظل متوهج"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(QColor(color))
        shadow.setOffset(offset[0], offset[1])
        widget.setGraphicsEffect(shadow)
        return shadow
    
    @staticmethod
    def add_drop_shadow(widget, blur_radius=15, offset=(0, 5), opacity=80):
        """إضافة ظل منسدل"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(QColor(0, 0, 0, opacity))
        shadow.setOffset(offset[0], offset[1])
        widget.setGraphicsEffect(shadow)
        return shadow
    
    @staticmethod
    def add_inner_shadow(widget, color="#000000", blur_radius=10):
        """إضافة ظل داخلي"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(blur_radius)
        shadow.setColor(QColor(color))
        shadow.setOffset(0, 0)
        widget.setGraphicsEffect(shadow)
        return shadow

class LoadingAnimations:
    """أنيميشن التحميل"""
    
    @staticmethod
    def create_loading_dots(parent_widget):
        """إنشاء نقاط تحميل متحركة"""
        from PyQt5.QtWidgets import QLabel, QHBoxLayout
        
        container = QHBoxLayout()
        dots = []
        
        for i in range(3):
            dot = QLabel("●")
            dot.setStyleSheet("""
                QLabel {
                    color: #3498db;
                    font-size: 20px;
                    margin: 0px 5px;
                }
            """)
            dots.append(dot)
            container.addWidget(dot)
        
        # أنيميشن النقاط
        animations = []
        for i, dot in enumerate(dots):
            effect = QGraphicsOpacityEffect()
            dot.setGraphicsEffect(effect)
            
            animation = QPropertyAnimation(effect, b"opacity")
            animation.setDuration(600)
            animation.setStartValue(0.3)
            animation.setEndValue(1.0)
            animation.setEasingCurve(QEasingCurve.InOutSine)
            animation.setLoopCount(-1)
            
            # تأخير مختلف لكل نقطة
            QTimer.singleShot(i * 200, animation.start)
            animations.append(animation)
        
        return container, animations
    
    @staticmethod
    def create_progress_bar_animation(progress_bar):
        """أنيميشن شريط التقدم"""
        animation = QPropertyAnimation(progress_bar, b"value")
        animation.setDuration(2000)
        animation.setStartValue(0)
        animation.setEndValue(100)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        animation.start()
        
        return animation

class TransitionEffects:
    """تأثيرات الانتقال بين الصفحات"""
    
    @staticmethod
    def slide_transition(old_widget, new_widget, direction="left"):
        """انتقال بالانزلاق"""
        # إخفاء الويدجت القديم
        old_animation = AnimationManager.slide_out_to_left(old_widget) if direction == "left" else AnimationManager.slide_out_to_right(old_widget)
        
        # إظهار الويدجت الجديد
        new_animation = AnimationManager.slide_in_from_right(new_widget) if direction == "left" else AnimationManager.slide_in_from_left(new_widget)
        
        return old_animation, new_animation
    
    @staticmethod
    def fade_transition(old_widget, new_widget):
        """انتقال بالتلاشي"""
        old_animation = AnimationManager.fade_out(old_widget)
        new_animation = AnimationManager.fade_in(new_widget)
        
        return old_animation, new_animation

class InteractiveEffects:
    """تأثيرات تفاعلية"""
    
    @staticmethod
    def button_press_effect(button):
        """تأثير الضغط على الزر"""
        original_size = button.size()
        
        # تصغير
        shrink_animation = QPropertyAnimation(button, b"size")
        shrink_animation.setDuration(100)
        shrink_animation.setStartValue(original_size)
        shrink_animation.setEndValue(original_size * 0.95)
        shrink_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # إعادة للحجم الأصلي
        expand_animation = QPropertyAnimation(button, b"size")
        expand_animation.setDuration(100)
        expand_animation.setStartValue(original_size * 0.95)
        expand_animation.setEndValue(original_size)
        expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # تسلسل الأنيميشن
        sequence = QSequentialAnimationGroup()
        sequence.addAnimation(shrink_animation)
        sequence.addAnimation(expand_animation)
        sequence.start()
        
        return sequence
    
    @staticmethod
    def hover_glow_effect(widget, glow_color="#3498db"):
        """تأثير التوهج عند التمرير"""
        def on_enter():
            ShadowEffects.add_glow_shadow(widget, glow_color, 25, (0, 0))
        
        def on_leave():
            ShadowEffects.add_drop_shadow(widget)
        
        widget.enterEvent = lambda event: on_enter()
        widget.leaveEvent = lambda event: on_leave()

class NotificationEffects:
    """تأثيرات الإشعارات"""
    
    @staticmethod
    def success_notification_animation(widget):
        """أنيميشن إشعار النجاح"""
        # تغيير اللون إلى الأخضر
        widget.setStyleSheet("""
            QWidget {
                background-color: #27ae60;
                color: white;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        # أنيميشن الظهور والاختفاء
        fade_in = AnimationManager.fade_in(widget, 300)
        
        # اختفاء تلقائي بعد 3 ثوان
        QTimer.singleShot(3000, lambda: AnimationManager.fade_out(widget, 300))
        
        return fade_in
    
    @staticmethod
    def error_notification_animation(widget):
        """أنيميشن إشعار الخطأ"""
        # تغيير اللون إلى الأحمر
        widget.setStyleSheet("""
            QWidget {
                background-color: #e74c3c;
                color: white;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        # أنيميشن الاهتزاز
        original_pos = widget.pos()
        shake_animation = QPropertyAnimation(widget, b"pos")
        shake_animation.setDuration(500)
        shake_animation.setLoopCount(3)
        
        # مواضع الاهتزاز
        positions = [
            original_pos,
            original_pos + QPoint(10, 0),
            original_pos - QPoint(10, 0),
            original_pos
        ]
        
        for i, pos in enumerate(positions):
            shake_animation.setKeyValueAt(i / (len(positions) - 1), pos)
        
        shake_animation.start()
        
        return shake_animation

def apply_modern_theme(app):
    """تطبيق الثيم العصري على التطبيق"""
    app.setStyleSheet("""
        QApplication {
            font-family: "Segoe UI", "Arial", sans-serif;
        }
        
        QToolTip {
            background-color: #2c3e50;
            color: white;
            border: 1px solid #34495e;
            border-radius: 5px;
            padding: 8px;
            font-size: 12px;
        }
        
        QScrollBar:vertical {
            background-color: #ecf0f1;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #bdc3c7;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #95a5a6;
        }
    """)
