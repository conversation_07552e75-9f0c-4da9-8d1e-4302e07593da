#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة المصاريف الشخصية والديون - النسخة المبسطة
تطبيق سطح مكتب باستخدام PyQt5 وقاعدة بيانات SQLite
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QStackedWidget, QLabel,
                             QFrame, QMessageBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor
from database import DatabaseManager
from expense_widget import ExpenseWidget
from debt_widget import DebtWidget
from reports_widget import ReportsWidget
from datetime import date

class SimpleMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """إعداد واجهة المستخدم المبسطة"""
        self.setWindowTitle("💰 مدير المصاريف الشخصية")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # إعداد الخط العربي
        font = QFont("Arial", 11)
        self.setFont(font)
        
        # إعداد الأنماط المبسطة
        self.setup_simple_style()
        
        # إنشاء الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # القائمة الجانبية المبسطة
        self.create_simple_sidebar()
        main_layout.addWidget(self.sidebar)
        
        # المحتوى الرئيسي
        self.create_main_content()
        main_layout.addWidget(self.main_content, 1)
        
        # إعداد الصفحات
        self.setup_pages()
        
        # عرض الصفحة الرئيسية
        self.show_dashboard()
        
    def setup_simple_style(self):
        """إعداد الأنماط المبسطة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                color: #2c3e50;
                font-weight: 500;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #ddd;
            }
        """)
        
    def create_simple_sidebar(self):
        """إنشاء القائمة الجانبية المبسطة"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(250)
        self.sidebar.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 10px;
                margin: 5px;
            }
            QPushButton {
                background-color: transparent;
                color: #ecf0f1;
                text-align: left;
                padding: 15px 20px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #3498db;
            }
            QPushButton:checked {
                background-color: #2980b9;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout(self.sidebar)
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(5)
        
        # عنوان التطبيق
        title = QLabel("💰 مدير المصاريف")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
                background-color: #2980b9;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # أزرار القائمة
        self.menu_buttons = {}
        
        buttons_data = [
            ("dashboard", "🏠 الرئيسية", self.show_dashboard),
            ("expenses", "💳 المصاريف", self.show_expenses),
            ("debts", "📊 الديون", self.show_debts),
            ("reports", "📈 التقارير", self.show_reports),
            ("settings", "⚙️ الإعدادات", self.show_settings)
        ]
        
        for key, text, callback in buttons_data:
            btn = QPushButton(text)
            btn.setCheckable(True)
            btn.clicked.connect(callback)
            self.menu_buttons[key] = btn
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.1 المبسط")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 11px;
                padding: 10px;
            }
        """)
        layout.addWidget(version_label)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        self.main_content = QStackedWidget()
        self.main_content.setStyleSheet("""
            QStackedWidget {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #ddd;
                margin: 5px;
            }
        """)
        
    def setup_pages(self):
        """إعداد صفحات التطبيق"""
        # الصفحة الرئيسية
        self.dashboard_widget = self.create_simple_dashboard()
        self.main_content.addWidget(self.dashboard_widget)
        
        # صفحة المصاريف
        self.expense_widget = ExpenseWidget(self.db)
        self.main_content.addWidget(self.expense_widget)
        
        # صفحة الديون
        self.debt_widget = DebtWidget(self.db)
        self.main_content.addWidget(self.debt_widget)
        
        # صفحة التقارير
        self.reports_widget = ReportsWidget(self.db)
        self.main_content.addWidget(self.reports_widget)
        
        # صفحة الإعدادات
        self.settings_widget = self.create_settings()
        self.main_content.addWidget(self.settings_widget)
        
    def create_simple_dashboard(self):
        """إنشاء الصفحة الرئيسية المبسطة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title = QLabel("🏠 لوحة التحكم الرئيسية")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # بطاقات الإحصائيات المبسطة
        stats_layout = QHBoxLayout()
        
        # إجمالي المصاريف هذا الشهر
        today = date.today()
        start_date = date(today.year, today.month, 1)
        monthly_expenses = self.db.get_expenses(start_date=start_date)
        monthly_total = sum(expense[1] for expense in monthly_expenses)
        
        stats_layout.addWidget(self.create_simple_stat_card("💳", "مصاريف الشهر", f"{monthly_total:,.2f} ريال", "#e74c3c"))
        
        # عدد الديون المعلقة
        pending_debts = self.db.get_debts(is_paid=False)
        stats_layout.addWidget(self.create_simple_stat_card("📊", "ديون معلقة", str(len(pending_debts)), "#f39c12"))
        
        # إجمالي الديون لي
        my_debts = [debt for debt in pending_debts if debt[4] == 'لي']
        my_debts_total = sum(debt[2] for debt in my_debts)
        stats_layout.addWidget(self.create_simple_stat_card("💰", "ديون لي", f"{my_debts_total:,.2f} ريال", "#27ae60"))
        
        # إجمالي الديون عليّ
        on_me_debts = [debt for debt in pending_debts if debt[4] == 'عليّ']
        on_me_debts_total = sum(debt[2] for debt in on_me_debts)
        stats_layout.addWidget(self.create_simple_stat_card("📉", "ديون عليّ", f"{on_me_debts_total:,.2f} ريال", "#8e44ad"))
        
        layout.addLayout(stats_layout)
        
        # أزرار الإجراءات السريعة
        buttons_layout = QHBoxLayout()
        
        add_expense_btn = QPushButton("💳 إضافة مصروف جديد")
        add_expense_btn.setStyleSheet("QPushButton { background-color: #27ae60; padding: 15px; }")
        add_expense_btn.clicked.connect(self.show_expenses)
        buttons_layout.addWidget(add_expense_btn)
        
        add_debt_btn = QPushButton("📊 إضافة دين جديد")
        add_debt_btn.setStyleSheet("QPushButton { background-color: #e74c3c; padding: 15px; }")
        add_debt_btn.clicked.connect(self.show_debts)
        buttons_layout.addWidget(add_debt_btn)
        
        view_reports_btn = QPushButton("📈 عرض التقارير")
        view_reports_btn.setStyleSheet("QPushButton { background-color: #3498db; padding: 15px; }")
        view_reports_btn.clicked.connect(self.show_reports)
        buttons_layout.addWidget(view_reports_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return widget
        
    def create_simple_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية مبسطة"""
        card = QFrame()
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 10px;
                border-left: 5px solid {color};
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 32px; color: {color}; margin-bottom: 10px;")
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 12px; color: #7f8c8d; margin-bottom: 5px;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        return card
        
    def create_settings(self):
        """إنشاء صفحة الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title = QLabel("⚙️ الإعدادات")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        settings_label = QLabel("صفحة الإعدادات قيد التطوير...")
        settings_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(settings_label)
        layout.addStretch()
        
        return widget
        
    def setup_timer(self):
        """إعداد مؤقت لتحديث البيانات"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_dashboard)
        self.timer.start(60000)  # تحديث كل دقيقة
        
    def refresh_dashboard(self):
        """تحديث بيانات الصفحة الرئيسية"""
        if self.main_content.currentWidget() == self.dashboard_widget:
            # إعادة إنشاء الصفحة الرئيسية
            self.main_content.removeWidget(self.dashboard_widget)
            self.dashboard_widget = self.create_simple_dashboard()
            self.main_content.insertWidget(0, self.dashboard_widget)
            self.main_content.setCurrentIndex(0)
    
    def set_active_button(self, active_key):
        """تحديد الزر النشط في القائمة الجانبية"""
        for key, button in self.menu_buttons.items():
            button.setChecked(key == active_key)
    
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        self.set_active_button("dashboard")
        self.main_content.setCurrentIndex(0)
        self.refresh_dashboard()
    
    def show_expenses(self):
        """عرض صفحة المصاريف"""
        self.set_active_button("expenses")
        self.main_content.setCurrentIndex(1)
        self.expense_widget.refresh_data()
    
    def show_debts(self):
        """عرض صفحة الديون"""
        self.set_active_button("debts")
        self.main_content.setCurrentIndex(2)
        self.debt_widget.refresh_data()
    
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.set_active_button("reports")
        self.main_content.setCurrentIndex(3)
        self.reports_widget.refresh_data()
    
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.set_active_button("settings")
        self.main_content.setCurrentIndex(4)

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد اتجاه النص للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط الافتراضي
    font = QFont("Arial", 11)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = SimpleMainWindow()
    window.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
