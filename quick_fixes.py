"""
إصلاحات سريعة للمشاكل الشائعة في التطبيق
"""

def fix_painter_warnings():
    """إصلاح تحذيرات QPainter"""
    # هذه التحذيرات عادة ما تكون بسبب تطبيق تأثيرات متعددة
    # على نفس الويدجت في نفس الوقت
    pass

def optimize_animations():
    """تحسين الأنيميشن لتجنب التداخل"""
    pass

def create_simple_stat_card(icon, title, value, color="#3498db"):
    """إنشاء بطاقة إحصائية مبسطة"""
    from PyQt5.QtWidgets import QFrame, QVBoxLayout, QHBoxLayout, QLabel
    from PyQt5.QtCore import Qt
    
    card = QFrame()
    card.setFixedSize(250, 120)
    
    card.setStyleSheet(f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 white, stop:1 #f8f9fa);
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 15px;
        }}
        QFrame:hover {{
            border: 2px solid {color};
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 white, stop:1 #f1f2f6);
        }}
    """)
    
    layout = QVBoxLayout(card)
    layout.setSpacing(8)
    
    # الصف العلوي
    top_row = QHBoxLayout()
    
    # الأيقونة
    icon_label = QLabel(icon)
    icon_label.setStyleSheet(f"""
        QLabel {{
            font-size: 28px;
            color: {color};
            background: {color}20;
            border-radius: 12px;
            padding: 8px;
            min-width: 50px;
            max-width: 50px;
        }}
    """)
    icon_label.setAlignment(Qt.AlignCenter)
    top_row.addWidget(icon_label)
    
    top_row.addStretch()
    
    layout.addLayout(top_row)
    
    # العنوان
    title_label = QLabel(title)
    title_label.setStyleSheet("""
        QLabel {
            font-size: 13px;
            color: #7f8c8d;
            font-weight: 500;
        }
    """)
    layout.addWidget(title_label)
    
    # القيمة
    value_label = QLabel(value)
    value_label.setStyleSheet(f"""
        QLabel {{
            font-size: 18px;
            font-weight: bold;
            color: {color};
        }}
    """)
    layout.addWidget(value_label)
    
    return card

def create_simple_button(text, color="#3498db"):
    """إنشاء زر مبسط"""
    from PyQt5.QtWidgets import QPushButton
    from PyQt5.QtCore import Qt
    
    btn = QPushButton(text)
    btn.setCursor(Qt.PointingHandCursor)
    btn.setMinimumHeight(45)
    
    btn.setStyleSheet(f"""
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {color}, stop:1 {color}80);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {color}E0, stop:1 {color});
        }}
        QPushButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {color}80, stop:1 {color}60);
        }}
    """)
    
    return btn

def apply_simple_theme(widget):
    """تطبيق ثيم مبسط"""
    widget.setStyleSheet("""
        QWidget {
            font-family: "Segoe UI", "Arial", sans-serif;
            font-size: 12px;
        }
        
        QLabel {
            color: #2c3e50;
        }
        
        QFrame {
            background-color: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        QScrollBar:vertical {
            background-color: #ecf0f1;
            width: 10px;
            border-radius: 5px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #bdc3c7;
            border-radius: 5px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #95a5a6;
        }
    """)

# دالة لإنشاء نسخة مبسطة من الواجهة الرئيسية
def create_simplified_dashboard():
    """إنشاء لوحة تحكم مبسطة"""
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame
    
    widget = QWidget()
    layout = QVBoxLayout(widget)
    layout.setContentsMargins(20, 20, 20, 20)
    layout.setSpacing(20)
    
    # عنوان مبسط
    title = QLabel("💰 مرحباً بك في مدير المصاريف")
    title.setStyleSheet("""
        QLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 20px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3498db, stop:1 #2980b9);
            color: white;
            border-radius: 15px;
            margin-bottom: 20px;
        }
    """)
    layout.addWidget(title)
    
    # بطاقات مبسطة
    stats_layout = QHBoxLayout()
    
    # يمكن إضافة البطاقات هنا
    stats_layout.addWidget(create_simple_stat_card("💳", "مصاريف الشهر", "0.00 ريال", "#e74c3c"))
    stats_layout.addWidget(create_simple_stat_card("📊", "ديون معلقة", "0", "#f39c12"))
    stats_layout.addWidget(create_simple_stat_card("💰", "ديون لي", "0.00 ريال", "#27ae60"))
    
    layout.addLayout(stats_layout)
    
    # أزرار سريعة
    buttons_layout = QHBoxLayout()
    
    buttons_layout.addWidget(create_simple_button("💳 إضافة مصروف", "#27ae60"))
    buttons_layout.addWidget(create_simple_button("📊 إضافة دين", "#e74c3c"))
    buttons_layout.addWidget(create_simple_button("📈 عرض التقارير", "#3498db"))
    
    layout.addLayout(buttons_layout)
    layout.addStretch()
    
    return widget

def get_safe_colors():
    """ألوان آمنة للاستخدام"""
    return {
        'primary': '#3498db',
        'success': '#27ae60',
        'danger': '#e74c3c',
        'warning': '#f39c12',
        'info': '#8e44ad',
        'light': '#ecf0f1',
        'dark': '#2c3e50',
        'white': '#ffffff',
        'gray': '#7f8c8d'
    }
